{"name": "h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build:test": "vite build --mode test", "build": "vite build --mode prod", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.1.0", "@vueuse/integrations": "^13.1.0", "aos": "2.3.4", "axios": "^1.8.4", "countup.js": "^2.8.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qrcode": "^1.5.4", "resize-observer-polyfill": "^1.5.1", "swiper": "^11.2.6", "typed.js": "^2.1.0", "vant": "^4.9.19", "vite-plugin-require": "^1.2.14", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "wujie": "^1.0.28", "wujie-vue3": "^1.0.28"}, "devDependencies": {"@rollup/plugin-replace": "^6.0.2", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-legacy": "6.1.1", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.2.0", "autoprefixer": "^10.4.20", "fast-glob": "^3.3.3", "postcss": "^8.4.49", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vconsole": "^3.15.1", "vite": "^6.3.2", "vite-plugin-svg-icons": "^2.0.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}