/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const $captchaIns: typeof import('./src/hooks/common.js')['$captchaIns']
  const $captchaValidate: typeof import('./src/hooks/common.js')['$captchaValidate']
  const $quotes_route: typeof import('./src/store/quotes.js')['$quotes_route']
  const $raise_fall_color: typeof import('./src/store/global.js')['$raise_fall_color']
  const $theme: typeof import('./src/store/global.js')['$theme']
  const $token: typeof import('./src/apis/axios.interceptors.js')['$token']
  const ACCOUNT_ROUTES: typeof import('./src/config/account.js')['ACCOUNT_ROUTES']
  const ACCOUNT_TYPE: typeof import('./src/config/account.js')['ACCOUNT_TYPE']
  const API_PATH: typeof import('./src/apis/index.js')['API_PATH']
  const CHART_TYPES: typeof import('./src/config/stock.js')['CHART_TYPES']
  const CURRENCY: typeof import('./src/config/account.js')['CURRENCY']
  const DEFAULT_LANGUAGE: typeof import('./src/config/common.js')['DEFAULT_LANGUAGE']
  const DEFAULT_STOCK_SORT: typeof import('./src/store/quotes.js')['DEFAULT_STOCK_SORT']
  const ENTRUST_STATUS_DICT: typeof import('./src/config/account.js')['ENTRUST_STATUS_DICT']
  const EffectScope: typeof import('vue')['EffectScope']
  const FETCH_INTERVAL_TYPE: typeof import('./src/config/common.js')['FETCH_INTERVAL_TYPE']
  const FETCH_METHOD: typeof import('./src/apis/fetch.js')['FETCH_METHOD']
  const FILE_TYPES: typeof import('./src/hooks/common.js')['FILE_TYPES']
  const FUTURES_CONFIG: typeof import('./src/config/stock.js')['FUTURES_CONFIG']
  const HK_MINUTES_RANGE: typeof import('./src/config/stock.js')['HK_MINUTES_RANGE']
  const JUMP_DICT: typeof import('./src/config/common.js')['JUMP_DICT']
  const LANGUAGE: typeof import('./src/i18n/index.js')['LANGUAGE']
  const LOCALE_CONFIG: typeof import('./src/config/common.js')['LOCALE_CONFIG']
  const MARKET_DEFAULT_SYMBOL: typeof import('./src/store/stock.js')['MARKET_DEFAULT_SYMBOL']
  const MINUTES_RANGE: typeof import('./src/config/stock.js')['MINUTES_RANGE']
  const OTP_TYPES: typeof import('./src/hooks/user.js')['OTP_TYPES']
  const OTP_VERIFY: typeof import('./src/hooks/user.js')['OTP_VERIFY']
  const RAISE_FALL_COLOR_CONFIG: typeof import('./src/config/common.js')['RAISE_FALL_COLOR_CONFIG']
  const REGULAR: typeof import('./src/config/common.js')['REGULAR']
  const SETTING_TYPES: typeof import('./src/hooks/common.js')['SETTING_TYPES']
  const SOCKET_ACTIONS: typeof import('./src/config/common.js')['SOCKET_ACTIONS']
  const SOCKET_EVENTS: typeof import('./src/config/common.js')['SOCKET_EVENTS']
  const SORT_CONFIG: typeof import('./src/config/common.js')['SORT_CONFIG']
  const STOCK_CONFIG: typeof import('./src/config/stock.js')['STOCK_CONFIG']
  const STOCK_ROUTE: typeof import('./src/config/stock.js')['STOCK_ROUTE']
  const THEME_CONFIG: typeof import('./src/config/common.js')['THEME_CONFIG']
  const TIME_FORMAT: typeof import('./src/config/common.js')['TIME_FORMAT']
  const US_MINUTES_RANGE: typeof import('./src/config/stock.js')['US_MINUTES_RANGE']
  const _useCountdown: typeof import('./src/hooks/common.js')['_useCountdown']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const accountActiveTab: typeof import('./src/store/account.js')['accountActiveTab']
  const api_delete: typeof import('./src/apis/fetch.js')['api_delete']
  const api_fetch: typeof import('./src/apis/fetch.js')['api_fetch']
  const api_get: typeof import('./src/apis/fetch.js')['api_get']
  const api_post: typeof import('./src/apis/fetch.js')['api_post']
  const api_put: typeof import('./src/apis/fetch.js')['api_put']
  const asyncComputed: typeof import('@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('@vueuse/core')['autoResetRef']
  const axiosInterceptors: typeof import('./src/apis/axios.interceptors.js')['default']
  const cancelTokens: typeof import('./src/apis/axios.interceptors.js')['cancelTokens']
  const components: typeof import('./src/components/index.js')['default']
  const computed: typeof import('vue')['computed']
  const computedAsync: typeof import('@vueuse/core')['computedAsync']
  const computedEager: typeof import('@vueuse/core')['computedEager']
  const computedInject: typeof import('@vueuse/core')['computedInject']
  const computedWithControl: typeof import('@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('@vueuse/core')['controlledRef']
  const createApp: typeof import('vue')['createApp']
  const createEventHook: typeof import('@vueuse/core')['createEventHook']
  const createGlobalState: typeof import('@vueuse/core')['createGlobalState']
  const createInjectionState: typeof import('@vueuse/core')['createInjectionState']
  const createPinia: typeof import('pinia')['createPinia']
  const createReactiveFn: typeof import('@vueuse/core')['createReactiveFn']
  const createRef: typeof import('@vueuse/core')['createRef']
  const createReusableTemplate: typeof import('@vueuse/core')['createReusableTemplate']
  const createSharedComposable: typeof import('@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('@vueuse/core')['createUnrefFn']
  const customRef: typeof import('vue')['customRef']
  const debouncedRef: typeof import('@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('@vueuse/core')['debouncedWatch']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const eagerComputed: typeof import('@vueuse/core')['eagerComputed']
  const effectScope: typeof import('vue')['effectScope']
  const extendRef: typeof import('@vueuse/core')['extendRef']
  const futuresInfoInitial: typeof import('./src/hooks/futures.js')['futuresInfoInitial']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const i18n: typeof import('./src/i18n/index.js')['default']
  const ignorableWatch: typeof import('@vueuse/core')['ignorableWatch']
  const inject: typeof import('vue')['inject']
  const injectLocal: typeof import('@vueuse/core')['injectLocal']
  const isDefined: typeof import('@vueuse/core')['isDefined']
  const isNotFirst: typeof import('./src/store/global.js')['isNotFirst']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const localeStorage: typeof import('./src/i18n/index.js')['localeStorage']
  const logout: typeof import('./src/hooks/user.js')['logout']
  const makeDestructurable: typeof import('@vueuse/core')['makeDestructurable']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const messageActiveTab: typeof import('./src/store/message.js')['messageActiveTab']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onChangeLanguage: typeof import('./src/i18n/index.js')['onChangeLanguage']
  const onClickOutside: typeof import('@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onElementRemoval: typeof import('@vueuse/core')['onElementRemoval']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onGetFuturesInfo: typeof import('./src/hooks/futures.js')['onGetFuturesInfo']
  const onGetStockInfo: typeof import('./src/hooks/stock.jsx')['onGetStockInfo']
  const onKeyStroke: typeof import('@vueuse/core')['onKeyStroke']
  const onLongPress: typeof import('@vueuse/core')['onLongPress']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onStartTyping: typeof import('@vueuse/core')['onStartTyping']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const pausableWatch: typeof import('@vueuse/core')['pausableWatch']
  const provide: typeof import('vue')['provide']
  const provideLocal: typeof import('@vueuse/core')['provideLocal']
  const reactify: typeof import('@vueuse/core')['reactify']
  const reactifyObject: typeof import('@vueuse/core')['reactifyObject']
  const reactive: typeof import('vue')['reactive']
  const reactiveComputed: typeof import('@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('@vueuse/core')['reactivePick']
  const readonly: typeof import('vue')['readonly']
  const record_range_presets: typeof import('./src/config/common.js')['record_range_presets']
  const ref: typeof import('vue')['ref']
  const refAutoReset: typeof import('@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('@vueuse/core')['refDebounced']
  const refDefault: typeof import('@vueuse/core')['refDefault']
  const refThrottled: typeof import('@vueuse/core')['refThrottled']
  const refWithControl: typeof import('@vueuse/core')['refWithControl']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const resolveRef: typeof import('@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('@vueuse/core')['resolveUnref']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showConfirmDialog: typeof import('vant/es')['showConfirmDialog']
  const showDialog: typeof import('vant/es')['showDialog']
  const showFailToast: typeof import('vant/es')['showFailToast']
  const showLoadingToast: typeof import('vant/es')['showLoadingToast']
  const showNotify: typeof import('vant/es')['showNotify']
  const showSuccessToast: typeof import('vant/es')['showSuccessToast']
  const spotActiveTab: typeof import('./src/store/account.js')['spotActiveTab']
  const stockCountryDict: typeof import('./src/config/stock.js')['stockCountryDict']
  const stockInfoInitial: typeof import('./src/hooks/stock.jsx')['stockInfoInitial']
  const stockMarketDict: typeof import('./src/config/stock.js')['stockMarketDict']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const syncRef: typeof import('@vueuse/core')['syncRef']
  const syncRefs: typeof import('@vueuse/core')['syncRefs']
  const templateRef: typeof import('@vueuse/core')['templateRef']
  const throttledRef: typeof import('@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('@vueuse/core')['throttledWatch']
  const toRaw: typeof import('vue')['toRaw']
  const toReactive: typeof import('@vueuse/core')['toReactive']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const tokenKey: typeof import('./src/apis/axios.interceptors.js')['tokenKey']
  const tradeDirectionDict: typeof import('./src/config/stock.js')['tradeDirectionDict']
  const tradeDirectionOptions: typeof import('./src/config/stock.js')['tradeDirectionOptions']
  const tradeTypeDict: typeof import('./src/config/stock.js')['tradeTypeDict']
  const tradeTypeOptions: typeof import('./src/config/stock.js')['tradeTypeOptions']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryOnBeforeMount: typeof import('@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('@vueuse/core')['tryOnUnmounted']
  const unref: typeof import('vue')['unref']
  const unrefElement: typeof import('@vueuse/core')['unrefElement']
  const until: typeof import('@vueuse/core')['until']
  const useAccountId: typeof import('./src/hooks/contract.js')['useAccountId']
  const useAccountStore: typeof import('./src/store/account.js')['useAccountStore']
  const useActiveElement: typeof import('@vueuse/core')['useActiveElement']
  const useAnimate: typeof import('@vueuse/core')['useAnimate']
  const useArrayDifference: typeof import('@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('@vueuse/core')['useArrayUnique']
  const useAsyncQueue: typeof import('@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBankStore: typeof import('./src/store/financial.js')['useBankStore']
  const useBase64: typeof import('@vueuse/core')['useBase64']
  const useBattery: typeof import('@vueuse/core')['useBattery']
  const useBluetooth: typeof import('@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('@vueuse/core')['useBroadcastChannel']
  const useBrowserLocation: typeof import('@vueuse/core')['useBrowserLocation']
  const useCached: typeof import('@vueuse/core')['useCached']
  const useCaptcha: typeof import('./src/hooks/common.js')['useCaptcha']
  const useClipboard: typeof import('@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('@vueuse/core')['useCloned']
  const useColorMode: typeof import('@vueuse/core')['useColorMode']
  const useConfirmDialog: typeof import('@vueuse/core')['useConfirmDialog']
  const useContractList: typeof import('./src/hooks/account.js')['useContractList']
  const useContractType: typeof import('./src/config/contract.js')['useContractType']
  const useCountdown: typeof import('@vueuse/core')['useCountdown']
  const useCounter: typeof import('@vueuse/core')['useCounter']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVar: typeof import('@vueuse/core')['useCssVar']
  const useCssVariable: typeof import('./src/hooks/common.js')['useCssVariable']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentContract: typeof import('./src/hooks/contract.js')['useCurrentContract']
  const useCurrentElement: typeof import('@vueuse/core')['useCurrentElement']
  const useCurrentLang: typeof import('./src/hooks/common.js')['useCurrentLang']
  const useCycleList: typeof import('@vueuse/core')['useCycleList']
  const useDark: typeof import('@vueuse/core')['useDark']
  const useDateFormat: typeof import('@vueuse/core')['useDateFormat']
  const useDealTable: typeof import('./src/hooks/account.js')['useDealTable']
  const useDebounce: typeof import('@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('@vueuse/core')['useDebouncedRefHistory']
  const useDeleteConfirm: typeof import('./src/hooks/common.js')['useDeleteConfirm']
  const useDeviceMotion: typeof import('@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('@vueuse/core')['useDocumentVisibility']
  const useDownload: typeof import('./src/hooks/common.js')['useDownload']
  const useDraggable: typeof import('@vueuse/core')['useDraggable']
  const useDropZone: typeof import('@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('@vueuse/core')['useElementHover']
  const useElementSize: typeof import('@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('@vueuse/core')['useElementVisibility']
  const useEntrustRevoke: typeof import('./src/hooks/stock.jsx')['useEntrustRevoke']
  const useEntrustTable: typeof import('./src/hooks/account.js')['useEntrustTable']
  const useEventBus: typeof import('@vueuse/core')['useEventBus']
  const useEventListener: typeof import('@vueuse/core')['useEventListener']
  const useEventSource: typeof import('@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('@vueuse/core')['useFavicon']
  const useFetch: typeof import('@vueuse/core')['useFetch']
  const useFetchLoading: typeof import('./src/hooks/common.js')['useFetchLoading']
  const useFileDialog: typeof import('@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('@vueuse/core')['useFileSystemAccess']
  const useFocus: typeof import('@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('@vueuse/core')['useFocusWithin']
  const useFormDisabled: typeof import('./src/hooks/form.js')['useFormDisabled']
  const useFps: typeof import('@vueuse/core')['useFps']
  const useFullscreen: typeof import('@vueuse/core')['useFullscreen']
  const useFuturesColumns: typeof import('./src/hooks/stock.jsx')['useFuturesColumns']
  const useFuturesList: typeof import('./src/hooks/stock.jsx')['useFuturesList']
  const useFuturesRedirect: typeof import('./src/hooks/stock.jsx')['useFuturesRedirect']
  const useFuturesStatus: typeof import('./src/hooks/futures.js')['useFuturesStatus']
  const useFuturesStore: typeof import('./src/store/futures.js')['useFuturesStore']
  const useGamepad: typeof import('@vueuse/core')['useGamepad']
  const useGeolocation: typeof import('@vueuse/core')['useGeolocation']
  const useGlobalStore: typeof import('./src/store/global.js')['useGlobalStore']
  const useI18n: typeof import('vue-i18n')['useI18n']
  const useId: typeof import('vue')['useId']
  const useIdle: typeof import('@vueuse/core')['useIdle']
  const useImage: typeof import('@vueuse/core')['useImage']
  const useIndexStore: typeof import('./src/store/quotes.js')['useIndexStore']
  const useIndustry: typeof import('./src/hooks/stock.jsx')['useIndustry']
  const useInfiniteScroll: typeof import('@vueuse/core')['useInfiniteScroll']
  const useIntersectionObserver: typeof import('@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('@vueuse/core')['useInterval']
  const useIntervalFetch: typeof import('./src/hooks/common.js')['useIntervalFetch']
  const useIntervalFn: typeof import('@vueuse/core')['useIntervalFn']
  const useKeyModifier: typeof import('@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('@vueuse/core')['useLastChanged']
  const useLink: typeof import('vue-router')['useLink']
  const useLocalStorage: typeof import('@vueuse/core')['useLocalStorage']
  const useLogout: typeof import('./src/hooks/user.js')['useLogout']
  const useMagicKeys: typeof import('@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('@vueuse/core')['useManualRefHistory']
  const useMarketStatus: typeof import('./src/hooks/stock.jsx')['useMarketStatus']
  const useMarketStatusStore: typeof import('./src/store/stock.js')['useMarketStatusStore']
  const useMarketStocks: typeof import('./src/hooks/stock.jsx')['useMarketStocks']
  const useMediaControls: typeof import('@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('@vueuse/core')['useMediaQuery']
  const useMemoize: typeof import('@vueuse/core')['useMemoize']
  const useMemory: typeof import('@vueuse/core')['useMemory']
  const useMessageStore: typeof import('./src/store/message.js')['useMessageStore']
  const useModel: typeof import('vue')['useModel']
  const useMounted: typeof import('@vueuse/core')['useMounted']
  const useMouse: typeof import('@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('@vueuse/core')['useMousePressed']
  const useMutationObserver: typeof import('@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('@vueuse/core')['useNetwork']
  const useNewsStore: typeof import('./src/store/news.js')['useNewsStore']
  const useNoticeStore: typeof import('./src/store/global.js')['useNoticeStore']
  const useNow: typeof import('@vueuse/core')['useNow']
  const useObjectUrl: typeof import('@vueuse/core')['useObjectUrl']
  const useOffsetPagination: typeof import('@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('@vueuse/core')['useOnline']
  const useOtp: typeof import('./src/hooks/user.js')['useOtp']
  const usePageLeave: typeof import('@vueuse/core')['usePageLeave']
  const usePagination: typeof import('./src/apis/fetch.js')['usePagination']
  const useParallax: typeof import('@vueuse/core')['useParallax']
  const useParentElement: typeof import('@vueuse/core')['useParentElement']
  const usePassword: typeof import('./src/hooks/account.js')['usePassword']
  const usePerformanceObserver: typeof import('@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('@vueuse/core')['usePermission']
  const usePointer: typeof import('@vueuse/core')['usePointer']
  const usePointerLock: typeof import('@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('@vueuse/core')['usePointerSwipe']
  const usePositionFetch: typeof import('./src/hooks/account.js')['usePositionFetch']
  const usePreferredColorScheme: typeof import('@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('@vueuse/core')['usePreferredReducedMotion']
  const usePreferredReducedTransparency: typeof import('@vueuse/core')['usePreferredReducedTransparency']
  const usePrevious: typeof import('@vueuse/core')['usePrevious']
  const useProfileStore: typeof import('./src/store/user.js')['useProfileStore']
  const useProtocolPopup: typeof import('./src/hooks/common.js')['useProtocolPopup']
  const useProtocolStore: typeof import('./src/store/global.js')['useProtocolStore']
  const useQuotesStore: typeof import('./src/store/quotes.js')['useQuotesStore']
  const useRafFn: typeof import('@vueuse/core')['useRafFn']
  const useRate: typeof import('./src/hooks/account.js')['useRate']
  const useRateStore: typeof import('./src/store/account.js')['useRateStore']
  const useRefHistory: typeof import('@vueuse/core')['useRefHistory']
  const useRequest: typeof import('./src/apis/fetch.js')['useRequest']
  const useResizeObserver: typeof import('@vueuse/core')['useResizeObserver']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSSRWidth: typeof import('@vueuse/core')['useSSRWidth']
  const useScreenOrientation: typeof import('@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('@vueuse/core')['useScreenSafeArea']
  const useScriptTag: typeof import('@vueuse/core')['useScriptTag']
  const useScroll: typeof import('@vueuse/core')['useScroll']
  const useScrollLock: typeof import('@vueuse/core')['useScrollLock']
  const useSessionStorage: typeof import('@vueuse/core')['useSessionStorage']
  const useShare: typeof import('@vueuse/core')['useShare']
  const useSlots: typeof import('vue')['useSlots']
  const useSocket: typeof import('./src/hooks/common.js')['useSocket']
  const useSorted: typeof import('@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('@vueuse/core')['useSpeechSynthesis']
  const useStepper: typeof import('@vueuse/core')['useStepper']
  const useStockCollect: typeof import('./src/hooks/stock.jsx')['useStockCollect']
  const useStockColumns: typeof import('./src/hooks/stock.jsx')['useStockColumns']
  const useStockRedirect: typeof import('./src/hooks/stock.jsx')['useStockRedirect']
  const useStockStore: typeof import('./src/store/stock.js')['useStockStore']
  const useStorage: typeof import('@vueuse/core')['useStorage']
  const useStorageAsync: typeof import('@vueuse/core')['useStorageAsync']
  const useStyleTag: typeof import('@vueuse/core')['useStyleTag']
  const useSupported: typeof import('@vueuse/core')['useSupported']
  const useSwipe: typeof import('@vueuse/core')['useSwipe']
  const useSysConfigStore: typeof import('./src/store/global.js')['useSysConfigStore']
  const useSystemSetting: typeof import('./src/hooks/common.js')['useSystemSetting']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTemplateRefsList: typeof import('@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('@vueuse/core')['useTextareaAutosize']
  const useThrottle: typeof import('@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('@vueuse/core')['useTimeoutPoll']
  const useTimestamp: typeof import('@vueuse/core')['useTimestamp']
  const useTitle: typeof import('@vueuse/core')['useTitle']
  const useToNumber: typeof import('@vueuse/core')['useToNumber']
  const useToString: typeof import('@vueuse/core')['useToString']
  const useTodayTrend: typeof import('./src/hooks/stock.jsx')['useTodayTrend']
  const useToggle: typeof import('@vueuse/core')['useToggle']
  const useTransition: typeof import('@vueuse/core')['useTransition']
  const useUpload: typeof import('./src/hooks/common.js')['useUpload']
  const useUrlSearchParams: typeof import('@vueuse/core')['useUrlSearchParams']
  const useUserMedia: typeof import('@vueuse/core')['useUserMedia']
  const useVModel: typeof import('@vueuse/core')['useVModel']
  const useVModels: typeof import('@vueuse/core')['useVModels']
  const useVibrate: typeof import('@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('@vueuse/core')['useVirtualList']
  const useWakeLock: typeof import('@vueuse/core')['useWakeLock']
  const useWalletStore: typeof import('./src/store/financial.js')['useWalletStore']
  const useWebNotification: typeof import('@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('@vueuse/core')['useWindowSize']
  const useWithdrawalStore: typeof import('./src/store/financial.js')['useWithdrawalStore']
  const utils_amount_color: typeof import('./src/utils/common.js')['utils_amount_color']
  const utils_big_amount_unit: typeof import('./src/utils/common.js')['utils_big_amount_unit']
  const utils_contract_name: typeof import('./src/utils/contract.js')['utils_contract_name']
  const utils_currency: typeof import('./src/utils/common.js')['utils_currency']
  const utils_decrypt: typeof import('./src/utils/common.js')['utils_decrypt']
  const utils_digit_precision: typeof import('./src/utils/common.js')['utils_digit_precision']
  const utils_encrypt: typeof import('./src/utils/common.js')['utils_encrypt']
  const utils_format_options: typeof import('./src/utils/common.js')['utils_format_options']
  const utils_get_os: typeof import('./src/utils/common.js')['utils_get_os']
  const utils_jump: typeof import('./src/utils/common.js')['utils_jump']
  const utils_link: typeof import('./src/utils/common.js')['utils_link']
  const utils_link2: typeof import('./src/utils/common.js')['utils_link2']
  const utils_mobile_privacy: typeof import('./src/utils/common.js')['utils_mobile_privacy']
  const utils_options_to_dict: typeof import('./src/utils/common.js')['utils_options_to_dict']
  const utils_speech_synthesis: typeof import('./src/utils/common.js')['utils_speech_synthesis']
  const utils_theme: typeof import('./src/utils/common.js')['utils_theme']
  const utils_time: typeof import('./src/utils/common.js')['utils_time']
  const watch: typeof import('vue')['watch']
  const watchArray: typeof import('@vueuse/core')['watchArray']
  const watchAtMost: typeof import('@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('@vueuse/core')['watchDeep']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchIgnorable: typeof import('@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('@vueuse/core')['watchOnce']
  const watchPausable: typeof import('@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const watchThrottled: typeof import('@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('@vueuse/core')['watchWithFilter']
  const whenever: typeof import('@vueuse/core')['whenever']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
