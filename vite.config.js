import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import { resolve } from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import replace from '@rollup/plugin-replace'
import { visualizer } from 'rollup-plugin-visualizer'
import legacy from '@vitejs/plugin-legacy'
import pkg from './package.json'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, resolve(process.cwd(), 'env'))
    return {
        envDir: 'env',
        server: {
            open: true, host: true, port: 9999, proxy: {
                '/api': {
                    // target: 'https://************:61301/',
                    target: 'https://h5.gpnow.xyz', changeOrigin: true,
                }, '/ws': {
                    target: 'wss://h5.gpnow.xyz', changeOrigin: true, ws: true,
                },
            },
        },
        define: {
            'import.meta.env.__APP_VERSION__': JSON.stringify(pkg.version),
            'G_THEME': JSON.stringify(env?.VITE_UI_COLOR_SCHEME),
            'G_TEMPLATE': JSON.stringify(env?.VITE_UI_TEMPLATE),
        },
        plugins: [
            vue(),
            vueJsx(),
            AutoImport({
                dts: true,
                imports: [
                    'vue',
                    'vue-router',
                    'vue-i18n',
                    'pinia',
                    '@vueuse/core',
                ],
                resolvers: [
                    VantResolver(),
                ],
                dirs: [
                    './src/apis',
                    './src/config',
                    './src/utils',
                    './src/store',
                    './src/hooks',
                    './src/i18n',
                    './src/components',
                ],
            }),
            Components({
                resolvers: [
                    VantResolver(),
                ],
            }),
            createSvgIconsPlugin({
                iconDirs: [
                    resolve(__dirname, `skins/themes/${env?.VITE_UI_COLOR_SCHEME}/svgs`),
                ],
                symbolId: 'icon-[dir]-[name]',
            }),
            visualizer({ open: true }),
            replace({
                preventAssignment: true,
                _TEMPLATE_: () => env?.VITE_UI_TEMPLATE,
                _THEME_: () => env?.VITE_UI_COLOR_SCHEME,
            }),
            legacy({
                targets: [ 'iOS >= 12' ],
                additionalLegacyPolyfills: [ 'regenerator-runtime/runtime' ],
                modernPolyfills: true,
            }),
        ],
        resolve: {
            alias: {
                '@': resolve(__dirname, 'src'),
                '@skins': resolve(__dirname, 'skins'),
                '@themes': resolve(__dirname, 'skins/themes'),
                '@temp': resolve(__dirname, 'skins/templates'),
            },
        },
        build: {
            sourceMap: mode !== 'prod',
            // rollupOptions: {
            //     output: {
            //         entryFileNames: 'index.js',
            //         chunkFileNames: 'index.js',
            //         assetFileNames: 'index.[ext]',
            //     },
            // },
        },
    }
})
