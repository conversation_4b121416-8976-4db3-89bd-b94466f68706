<svg width="91" height="91" viewBox="0 0 91 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13146)">
<path d="M35.7144 52.7328L17.3069 55.3394C16.7623 55.4165 16.2676 55.6869 15.9304 56.0919L2.71158 71.9654C1.70181 73.178 2.52314 74.9027 4.16073 75.0084L16.9487 75.8343C17.7874 75.8885 18.4958 76.4009 18.7442 77.1331L22.1322 87.1184C22.6296 88.5842 24.7349 88.8901 25.7702 87.6469L38.8976 71.883C39.2137 71.5034 39.3682 71.0332 39.3313 70.5633L38.0631 54.4382C37.9771 53.3443 36.903 52.5645 35.7144 52.7328Z" fill="url(#paint0_linear_767_13146)"/>
</g>
<g filter="url(#filter1_ii_767_13146)">
<path d="M53.4712 50.767L72.4189 54.0257C72.9379 54.115 73.4057 54.38 73.7286 54.7677L87.9657 71.8641C88.9927 73.0974 88.1241 74.8484 86.4546 74.9103L73.5109 75.3904C72.6241 75.4232 71.8712 75.9685 71.6354 76.7485L68.5524 86.9481C68.0972 88.4543 65.9498 88.7977 64.8965 87.5328L50.762 70.5596C50.46 70.197 50.3051 69.7512 50.3246 69.3011L51.0518 52.5232C51.1014 51.3774 52.2352 50.5544 53.4712 50.767Z" fill="url(#paint1_linear_767_13146)"/>
</g>
<path d="M44.0687 0.598145L61.5645 10.0537L79.3054 19.1225L79.0603 37.6469L79.3054 56.1713L61.5645 65.2401L44.0687 74.6957L26.5729 65.2401L8.832 56.1713L9.07716 37.6469L8.832 19.1225L26.5729 10.0537L44.0687 0.598145Z" fill="url(#paint2_linear_767_13146)"/>
<g filter="url(#filter2_ii_767_13146)">
<path d="M44.0687 5.11621L59.4308 13.4186L75.0082 21.3815L74.793 37.6468L75.0082 53.9121L59.4308 61.875L44.0687 70.1774L28.7065 61.875L13.1291 53.9121L13.3444 37.6468L13.1291 21.3815L28.7065 13.4186L44.0687 5.11621Z" fill="url(#paint3_linear_767_13146)"/>
</g>
<g filter="url(#filter3_ii_767_13146)">
<path d="M44.2175 9.63428L57.5101 16.8182L70.9888 23.7083L70.8026 37.7823L70.9888 51.8563L57.5101 58.7464L44.2175 65.9303L30.925 58.7464L17.4462 51.8563L17.6325 37.7823L17.4462 23.7083L30.925 16.8182L44.2175 9.63428Z" fill="url(#paint4_linear_767_13146)"/>
</g>
<g filter="url(#filter4_d_767_13146)">
<path d="M33.7311 37.647L29.1807 23.189H32.6487L35.7637 35.0976L38.768 23.189H42.0817L37.6856 37.647H33.7311Z" fill="url(#paint5_linear_767_13146)"/>
</g>
<g filter="url(#filter5_d_767_13146)">
<path d="M44.0664 37.6464V23.189H47.0436V37.647L44.0664 37.6464Z" fill="url(#paint6_linear_767_13146)"/>
</g>
<g filter="url(#filter6_d_767_13146)">
<path d="M55.1384 23.189C59.3469 23.189 61.9291 24.6932 61.9291 27.8482C61.9291 31.191 59.2031 32.7162 55.4246 32.7162H53.5354V37.647H50.0205V23.189H55.1384ZM55.1384 30.4599C57.0756 30.4599 58.2945 29.8119 58.2945 27.8482C58.2945 26.1561 57.1468 25.4034 55.0905 25.4034H53.5361V30.4599H55.1384Z" fill="url(#paint7_linear_767_13146)"/>
</g>
<g filter="url(#filter7_d_767_13146)">
<path d="M44.5433 55.7195C43.6865 55.7195 42.8858 55.5894 42.1414 55.3294C41.4109 55.0694 40.821 54.6923 40.3715 54.1982C39.922 53.7042 39.6973 53.1061 39.6973 52.404C39.6973 51.7799 39.8799 51.2013 40.2451 50.6682C40.6243 50.1352 41.1089 49.6801 41.6989 49.3031C42.3029 48.926 42.935 48.666 43.5952 48.5229L44.1219 48.8545C43.532 49.2185 43.1316 49.6866 42.9209 50.2587C42.7102 50.8308 42.6049 51.3963 42.6049 51.9554C42.6049 52.9046 42.8296 53.6522 43.2791 54.1982C43.7286 54.7443 44.2764 55.0174 44.9226 55.0174C45.6249 55.0174 46.1235 54.8028 46.4185 54.3738C46.7275 53.9317 46.8821 53.3531 46.8821 52.638C46.8821 52.118 46.7346 51.6824 46.4396 51.3313C46.1446 50.9803 45.7724 50.6747 45.3229 50.4147C44.8875 50.1417 44.4309 49.8816 43.9534 49.6346C43.3494 49.3096 42.7594 48.952 42.1835 48.5619C41.6216 48.1719 41.1581 47.7298 40.7929 47.2358C40.4277 46.7287 40.2451 46.1436 40.2451 45.4805C40.2451 44.8824 40.4136 44.3363 40.7508 43.8423C41.1019 43.3352 41.6287 42.9321 42.331 42.6331C43.0474 42.3211 43.9463 42.165 45.0279 42.165C45.8005 42.165 46.4958 42.2821 47.1138 42.5161C47.7459 42.7501 48.2446 43.0882 48.6098 43.5302C48.989 43.9593 49.1787 44.4794 49.1787 45.0905C49.1787 45.6235 49.0101 46.1176 48.673 46.5727C48.3499 47.0147 47.9215 47.3983 47.3877 47.7233C46.854 48.0354 46.2781 48.2629 45.66 48.4059L45.3229 48.1524C45.7864 47.7883 46.1235 47.3268 46.3342 46.7677C46.5449 46.1956 46.6503 45.6495 46.6503 45.1295C46.6503 44.4144 46.4817 43.8748 46.1446 43.5107C45.8075 43.1337 45.3229 42.9452 44.6908 42.9452C44.3958 42.9452 44.1008 43.0037 43.8059 43.1207C43.5109 43.2377 43.2581 43.4327 43.0474 43.7058C42.8507 43.9658 42.7524 44.3298 42.7524 44.7979C42.7524 45.175 42.8928 45.526 43.1738 45.8511C43.4547 46.1761 43.8129 46.4817 44.2483 46.7677C44.6838 47.0407 45.1403 47.3008 45.6179 47.5478C46.25 47.8728 46.875 48.2434 47.4931 48.6595C48.1111 49.0625 48.6168 49.5371 49.0101 50.0832C49.4175 50.6162 49.6211 51.2403 49.6211 51.9554C49.6211 52.6965 49.3964 53.3531 48.9469 53.9252C48.5115 54.4843 47.9145 54.9263 47.156 55.2514C46.3975 55.5634 45.5266 55.7195 44.5433 55.7195Z" fill="url(#paint8_linear_767_13146)"/>
</g>
<defs>
<filter id="filter0_ii_767_13146" x="2.26953" y="48.71" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13146"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13146" result="effect2_innerShadow_767_13146"/>
</filter>
<filter id="filter1_ii_767_13146" x="50.3232" y="46.7334" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13146"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13146" result="effect2_innerShadow_767_13146"/>
</filter>
<filter id="filter2_ii_767_13146" x="13.1289" y="1.11621" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13146"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13146" result="effect2_innerShadow_767_13146"/>
</filter>
<filter id="filter3_ii_767_13146" x="17.4463" y="4.63428" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13146"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13146" result="effect2_innerShadow_767_13146"/>
</filter>
<filter id="filter4_d_767_13146" x="9.18066" y="13.189" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13146"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13146" result="shape"/>
</filter>
<filter id="filter5_d_767_13146" x="24.0664" y="13.189" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13146"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13146" result="shape"/>
</filter>
<filter id="filter6_d_767_13146" x="30.0205" y="13.189" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13146"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13146" result="shape"/>
</filter>
<filter id="filter7_d_767_13146" x="19.6973" y="32.165" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13146"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13146" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13146" x1="37.3586" y1="52.0456" x2="14.7989" y2="84.7193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13146" x1="51.6912" y1="49.99" x2="75.7649" y2="84.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13146" x1="44.0687" y1="0.598144" x2="44.0687" y2="74.807" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13146" x1="44.0687" y1="5.11621" x2="44.0687" y2="70.1774" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13146" x1="44.2175" y1="9.63428" x2="44.2175" y2="65.9303" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13146" x1="35.6312" y1="23.189" x2="35.6312" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13146" x1="45.555" y1="23.189" x2="45.555" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13146" x1="55.9748" y1="23.189" x2="55.9748" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13146" x1="44.6592" y1="42.165" x2="44.6592" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
