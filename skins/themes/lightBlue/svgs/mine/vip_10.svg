<svg width="91" height="91" viewBox="0 0 91 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13170)">
<path d="M36.0952 52.7328L17.6878 55.3394C17.1431 55.4165 16.6484 55.6869 16.3112 56.0919L3.09244 71.9654C2.08267 73.178 2.904 74.9027 4.54159 75.0084L17.3296 75.8343C18.1682 75.8885 18.8766 76.4009 19.1251 77.1331L22.5131 87.1184C23.0104 88.5842 25.1157 88.8901 26.151 87.6469L39.2784 71.883C39.5946 71.5034 39.7491 71.0332 39.7121 70.5633L38.4439 54.4382C38.3579 53.3443 37.2839 52.5645 36.0952 52.7328Z" fill="url(#paint0_linear_767_13170)"/>
</g>
<g filter="url(#filter1_ii_767_13170)">
<path d="M53.852 50.767L72.7998 54.0257C73.3188 54.115 73.7866 54.38 74.1094 54.7677L88.3466 71.8641C89.3736 73.0974 88.505 74.8484 86.8355 74.9103L73.8917 75.3904C73.005 75.4232 72.2521 75.9685 72.0163 76.7485L68.9333 86.9481C68.478 88.4543 66.3307 88.7977 65.2773 87.5328L51.1429 70.5596C50.8409 70.197 50.6859 69.7512 50.7054 69.3011L51.4326 52.5232C51.4823 51.3774 52.6161 50.5544 53.852 50.767Z" fill="url(#paint1_linear_767_13170)"/>
</g>
<path d="M44.4496 0.598145L61.9453 10.0537L79.6863 19.1225L79.4411 37.6469L79.6863 56.1713L61.9453 65.2401L44.4496 74.6957L26.9538 65.2401L9.21286 56.1713L9.45802 37.6469L9.21286 19.1225L26.9538 10.0537L44.4496 0.598145Z" fill="url(#paint2_linear_767_13170)"/>
<g filter="url(#filter2_ii_767_13170)">
<path d="M44.4496 5.11621L59.8117 13.4186L75.3891 21.3815L75.1738 37.6468L75.3891 53.9121L59.8117 61.875L44.4496 70.1774L29.0874 61.875L13.51 53.9121L13.7253 37.6468L13.51 21.3815L29.0874 13.4186L44.4496 5.11621Z" fill="url(#paint3_linear_767_13170)"/>
</g>
<g filter="url(#filter3_ii_767_13170)">
<path d="M44.5984 9.63428L57.8909 16.8182L71.3697 23.7083L71.1834 37.7823L71.3697 51.8563L57.8909 58.7464L44.5984 65.9303L31.3059 58.7464L17.8271 51.8563L18.0133 37.7823L17.8271 23.7083L31.3059 16.8182L44.5984 9.63428Z" fill="url(#paint4_linear_767_13170)"/>
</g>
<g filter="url(#filter4_d_767_13170)">
<path d="M34.112 37.647L29.5615 23.189H33.0295L36.1446 35.0976L39.1489 23.189H42.4625L38.0665 37.647H34.112Z" fill="url(#paint5_linear_767_13170)"/>
</g>
<g filter="url(#filter5_d_767_13170)">
<path d="M44.4473 37.6464V23.189H47.4244V37.647L44.4473 37.6464Z" fill="url(#paint6_linear_767_13170)"/>
</g>
<g filter="url(#filter6_d_767_13170)">
<path d="M55.5193 23.189C59.7278 23.189 62.31 24.6932 62.31 27.8482C62.31 31.191 59.584 32.7162 55.8055 32.7162H53.9162V37.647H50.4014V23.189H55.5193ZM55.5193 30.4599C57.4565 30.4599 58.6753 29.8119 58.6753 27.8482C58.6753 26.1561 57.5277 25.4034 55.4713 25.4034H53.9169V30.4599H55.5193Z" fill="url(#paint7_linear_767_13170)"/>
</g>
<g filter="url(#filter7_d_767_13170)">
<path d="M38.709 55.5625V55.131L39.6136 54.9152C39.8856 54.8498 40.0866 54.7714 40.2167 54.6798C40.3586 54.5883 40.4532 54.4641 40.5005 54.3071C40.5478 54.1371 40.5714 53.9148 40.5714 53.6402V44.0481C40.5714 43.8651 40.53 43.7278 40.4472 43.6362C40.3645 43.5447 40.2107 43.5054 39.9861 43.5185L38.709 43.5774V43.0085L43.0546 42.2239V53.6402C43.0546 53.9148 43.0783 54.1371 43.1256 54.3071C43.1729 54.4641 43.2616 54.5883 43.3916 54.6798C43.5335 54.7714 43.7405 54.8498 44.0125 54.9152L44.9171 55.131V55.5625H38.709Z" fill="url(#paint8_linear_767_13170)"/>
<path d="M50.376 55.7195C49.5719 55.7195 48.8802 55.5429 48.3007 55.1898C47.7331 54.8237 47.2601 54.3267 46.8817 53.699C46.5152 53.0583 46.2432 52.326 46.0658 51.5021C45.8885 50.6782 45.7998 49.7955 45.7998 48.854C45.7998 47.5986 45.9594 46.4674 46.2787 45.4605C46.5979 44.4535 47.0946 43.6558 47.7686 43.0674C48.4426 42.4658 49.3118 42.165 50.376 42.165C51.4284 42.165 52.2916 42.4658 52.9657 43.0674C53.6397 43.6558 54.1363 44.4535 54.4556 45.4605C54.7749 46.4674 54.9345 47.5986 54.9345 48.854C54.9345 49.7955 54.8458 50.6782 54.6684 51.5021C54.4911 52.326 54.2132 53.0583 53.8348 53.699C53.4682 54.3267 52.9952 54.8237 52.4158 55.1898C51.8482 55.5429 51.1683 55.7195 50.376 55.7195ZM50.3583 55.0329C50.784 55.0329 51.1269 54.8694 51.387 54.5425C51.6472 54.2156 51.8423 53.7644 51.9724 53.189C52.1143 52.6136 52.203 51.9598 52.2384 51.2275C52.2857 50.4821 52.3094 49.6909 52.3094 48.854C52.3094 48.0301 52.2857 47.2586 52.2384 46.5393C52.1911 45.807 52.0965 45.1728 51.9546 44.6366C51.8246 44.0874 51.6294 43.6558 51.3693 43.342C51.121 43.0281 50.7899 42.8712 50.376 42.8712C49.9503 42.8712 49.6074 43.0281 49.3472 43.342C49.0989 43.6558 48.9038 44.0874 48.7619 44.6366C48.6318 45.1728 48.5431 45.807 48.4958 46.5393C48.4485 47.2586 48.4249 48.0301 48.4249 48.854C48.4249 49.6909 48.4426 50.4821 48.4781 51.2275C48.5254 51.9598 48.6141 52.6136 48.7442 53.189C48.8861 53.7644 49.0812 54.2156 49.3295 54.5425C49.5896 54.8694 49.9326 55.0329 50.3583 55.0329Z" fill="url(#paint9_linear_767_13170)"/>
</g>
<defs>
<filter id="filter0_ii_767_13170" x="2.65039" y="48.71" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13170"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13170" result="effect2_innerShadow_767_13170"/>
</filter>
<filter id="filter1_ii_767_13170" x="50.7041" y="46.7334" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13170"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13170" result="effect2_innerShadow_767_13170"/>
</filter>
<filter id="filter2_ii_767_13170" x="13.5098" y="1.11621" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13170"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13170" result="effect2_innerShadow_767_13170"/>
</filter>
<filter id="filter3_ii_767_13170" x="17.8271" y="4.63428" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13170"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13170" result="effect2_innerShadow_767_13170"/>
</filter>
<filter id="filter4_d_767_13170" x="9.56152" y="13.189" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13170"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13170" result="shape"/>
</filter>
<filter id="filter5_d_767_13170" x="24.4473" y="13.189" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13170"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13170" result="shape"/>
</filter>
<filter id="filter6_d_767_13170" x="30.4014" y="13.189" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13170"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13170" result="shape"/>
</filter>
<filter id="filter7_d_767_13170" x="18.709" y="32.165" width="56.2256" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13170"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13170" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13170" x1="37.7394" y1="52.0456" x2="15.1797" y2="84.7193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13170" x1="52.0721" y1="49.99" x2="76.1458" y2="84.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13170" x1="44.4496" y1="0.598144" x2="44.4496" y2="74.807" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13170" x1="44.4496" y1="5.11621" x2="44.4496" y2="70.1774" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13170" x1="44.5984" y1="9.63428" x2="44.5984" y2="65.9303" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13170" x1="36.012" y1="23.189" x2="36.012" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13170" x1="45.9358" y1="23.189" x2="45.9358" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13170" x1="56.3557" y1="23.189" x2="56.3557" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13170" x1="46.8217" y1="42.165" x2="46.8217" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint9_linear_767_13170" x1="46.8217" y1="42.165" x2="46.8217" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
