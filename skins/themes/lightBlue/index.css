@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --special: #E06822;
    --red: #C92C31;
    --green: #2cb274;
    --tag: #E9F0FD;
    --red-text: #FF0000;
    --futures-red: #DF4446;
    --futures-green: #00A488;
    --futures-switch: #4E5B83;
}

/* 浅色主题 */
.van-theme-light {
    --regular: #8897B8;
    --select: #269AFF;
    --tab-color: #2662FF;
    --primary: #269AFF; /* 主色调 */
    --page: #F4F7FE; /* 页面背景色 */
    --bg: var(--van-white); /* 背景色 */
    --border: #F7F8F8; /* 边框 */
    --active: var(--primary); /* 激活状态 */
    --title: #525A79; /* 标题文本 */
    --paragraph: #373737; /* 段落文本 */
    --text: #525A79; /* 普通文本 */
    --link: #269AFF; /* 链接 */
    --controller_bg: #F2F5FF; /* 输入控件背景色 */
    --input: #A0A4B9;
    --shadow: 0 4px 8px 0 rgba(53, 70, 119, .1); /* 阴影 */
    --card: var(--bg); /* Card */
    --text1: #373737;
    --text2: #000;
    --text3: #FFFFFF;
    --van-floating-bubble-background: var(--link);
    --van-popover-light-text-color: var(--select);
    --bg-1: #F1F4FF;
    --bg-2: #E1F1FF;
    --bg-3: #F4F7FE;
    --bg-4: #FFFFFF;
    --btn-bg: linear-gradient(to right, #1890FF, #2662FF);
}

/* 深色主题 */
.van-theme-dark {
    --tab-color: #8897B8;
    --regular: #8897B8;
    --select: #269AFF;
    --primary: #269AFF; /* 主色调 */
    --page: #160D00; /* 页面背景色 */
    --bg: #0F1233; /* 背景色 */
    --border: #212837; /* 边框 */
    --active: var(--primary); /* 激活状态 */
    --title: #AEB6C6; /* 标题文本 */
    --paragraph: #fff; /* 段落文本 */
    --text: #D3D6E2; /* 普通文本 */
    --link: #2662FF; /* 链接 */
    --controller_bg: #192440; /* 输入控件背景色 */
    --input: #525A79; /* 输入控件背景色 */
    --shadow: 0 4px 8px 0 rgba(11, 18, 36, .1); /* 阴影 */
    --card: rgba(31, 35, 41, .4); /* Card */
    --text1: #B9B9B9;
    --text2: #8C6450;
    --text3: #192440;
    --van-floating-bubble-background: var(--link);
    --van-popover-light-text-color: var(--select);
    --bg-1: #192440;
    --bg-2: #163E74;
    --bg-3: #04040B;
    --bg-4: #0F1233;
    --btn-bg: linear-gradient(to right, #1890FF, #2662FF);
}


@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: #e5e7eb;
    }
}

@layer components {
    .flex-center {
        @apply flex items-center justify-center;
    }

    .flex-middle {
        @apply flex items-center;
    }

    .flex-between {
        @apply flex items-center justify-between;
    }

    .bg-active {
        background-color: var(--active);
        border-color: var(--primary);
        color: var(--van-white);
    }
}

/* 页面容器 */
.with-header-container {
    /*
        Header --header-height
    */
    height: calc(100% - var(--header-height));
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px 10px 10px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.with-header-container__noPadding {
    /*
        Header --header-height
    */
    height: calc(100% - var(--header-height));
    overflow-x: hidden;
    overflow-y: auto;
}

.tab-container {
    height: calc(100% - var(--van-tabs-line-height));
    overflow-x: hidden;
    overflow-y: auto;
}

/* 数字字体 */
@font-face {
    font-family: 'digit'; /* 自定义字体名称 */
    src: url('/fonts/AkzidenzGrotesk-ExtraBoldCond.otf') format('opentype'); /* 路径和格式 */
    font-weight: normal; /* 字体粗细 */
    font-style: normal;  /* 字体样式 */
}

.font-digit {
    font-family: 'digit', sans-serif;
}

html,
body,
#app {
    @apply size-full overflow-hidden;
}

#app {
    @apply max-w-[640px] mx-auto text-text capitalize;
}

::-webkit-scrollbar {
    display: none;
}

/* 涨跌红绿 */
.red_green_trend {
    --raise: var(--red);
    --fall: var(--green);
}

.green_red_trend {
    --raise: var(--green);
    --fall: var(--red);
}

.van-theme-light #app {
    background: var(--page);
}

/* 阴影效果 */
.van-theme-light .custom-shadow {
    box-shadow: var(--shadow);
    backdrop-filter: blur(4px)
}

.van-theme-dark #app {
    background: var(--page);
    background-size: cover;

    /* Vant */
    --van-cell-group-background: var(--card);
    --van-cell-background: var(--card);

    /* Picker */
    --van-picker-mask-color: linear-gradient(180deg, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1)),
    linear-gradient(0deg, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1));

    /* Cell */
    --van-cell-border-color: #253559;

    /* Skeleton */
    --van-active-color: var(--input);
    --van-skeleton-paragraph-background: var(--input);
}

/* 阴影效果 */
.van-theme-dark .custom-shadow {
    backdrop-filter: blur(4px)
}

/* 深色主题 */

/* 主色背景 */
.primary-bg {
    background-image: var(--primary-bg);
}

#app {
    --header-height: 44px;

    /* Vant 配置 */
    /* 主色调 */
    --van-primary-color: var(--primary);

    --primary-bg: linear-gradient(270deg, #1890FF 0%, #2662FF 100%),
    linear-gradient(270deg, #1890FF 0%, #2662FF 100%),
    linear-gradient(90deg, #1890FF 0%, #2662FF 100%);

    /* Button */
    /*--van-button-primary-background: var(--primary-bg);*/
    --van-button-radius: 8px;
    --van-button-mini-height: 26px;
    --van-button-default-height: 40px;

    /* Tabs */
    --van-tabs-line-height: 40px;
    --van-tab-active-text-color: var(--primary);
    --van-tabs-bottom-bar-color: var(--link);
    --van-tabs-bottom-bar-height: 3px;
    --van-tab-text-color: var(--regular);
    --van-tabs-bottom-bar-width: 20px;
    --van-tabs-nav-background: transparent;
    --van-tabs-default-color: var(--link);

    /* Popover */
    --van-popover-action-height: 40px;

    /* Cell */
    --van-cell-text-color: var(--title);
    --van-cell-value-color: var(--text);
    --van-cell-value-font-size: 12px;
    --van-cell-large-title-font-size: 14px;
    --van-cell-large-vertical-padding: 14px;

    /* Grid */
    --van-grid-item-content-background: transprent;

    /* Picker */
    --van-picker-background: var(--bg);

    /* Field */
    --van-field-label-color: var(--title);

    /* Radio */
    --van-radio-checked-icon-color: var(--primary);

    /* Checkbox */
    --van-checkbox-checked-icon-color: var(--link);

    /* PasswordInput */
    --van-password-input-margin: 0;
    --van-dialog-confirm-button-text-color: var(--primary);
    --van-tab__text: var(--regular) !important;
}

/* 下载浮动按钮 */
.download.van-floating-bubble {
    --van-floating-bubble-background: var(--primary);
    --van-floating-bubble-border-radius: 16px;
    box-shadow: 0 4px 8px 0 #35467714;
    color: white;
    text-align: center;
    font-size: 12px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 英文文本大驼峰 */
.van-button,
.van-popup {
    text-transform: capitalize;
}

/* Button mini */
.van-button--mini {
    --van-button-radius: 4px;
    --van-button-mini-padding: 0 var(--van-padding-xs);
}

.van-button--primary {
    --van-button-border-width: 0;
}

/* Tab 改造 */
.van-tabs .van-tabs__wrap .van-tabs__nav--shrink {
    padding-left: 0;
    padding-right: 0;
    align-items: center;
}

.van-tabs .van-tabs__content {
    overflow-x: hidden;
    overflow-y: auto;
}

.van-tabs--line > .van-tabs__content {
    height: calc(100% - var(--van-tabs-line-height));
}

.van-tabs--card > .van-tabs__content {
    height: calc(100% - var(--van-tabs-card-height));
}

.van-tabs .van-tab__panel {
    height: 100%;
}

.van-tabs__nav--shrink.van-tabs__nav {
    gap: 20px;
}

/* .van-tab--shrink.van-tab {
    padding: 0;
} */

/* 骨架屏 */
/* #app .van-skeleton {
    padding: 0;
} */

/* Cell */
.van-cell-group {
    border-radius: 8px;
    overflow: hidden;
}

/* Popover */
.van-popover {
    --van-popover-action-width: 100%;
    box-shadow: var(--shadow);
}

/* Notify */
.van-notify--primary {
    color: var(--van-white);
    /*--van-notify-primary-background: var(--primary);*/
}

/* 小块 */
.marketBlock {
    color: var(--van-white);
    height: 16px;
    line-height: 16px;
    font-size: 10px;
    padding: 0 4px;
    border-radius: 2px;
}

:where(.marketBlock) {
    background: var(--primary);
}


.van-theme-light .van-notice-bar {
    background: white !important;
    border-radius: 0 !important;
    color: #525A79 !important;
}

.van-theme-dark .van-notice-bar {
    background: #0F1233 !important;
    color: #EBEEFB !important;
}

.van-calendar__selected-day {
    background: var(--primary) !important;
}

.van-button--primary {
    background: var(--primary) !important;
}

.van-dialog__confirm {
    color: var(--primary) !important;
}

/* 自定义伪元素样式 */
.skew-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -13px;
    width: 40px;
    height: 100%;
    border-radius: 5px;
    background: var(--skew-bg-color, #269AFF); /* 默认色 */
    z-index: -10;
}

.skew-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 5px;
    background: var(--skew-bg-color, #269AFF);
    transform: skewX(-25deg);
    z-index: -10;
}

/* 主体斜切背景（向右） */
.reverse-skew-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 10px;
    background: var(--skew-bg-color, #E1F1FF);
    transform: skewX(-25deg);
    z-index: -10;
    height: 100%;
}

/* 右边的矩形突出 */
.reverse-skew-btn::before {
    content: "";
    position: absolute;
    top: 0;
    right: -13px;
    width: 40px;
    height: 100%;
    border-radius: 5px;
    background: var(--skew-bg-color, #E1F1FF);
    z-index: -10;
}
