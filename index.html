<!doctype html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="referrer" content="no-referrer">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!-- 禁用电话自动识别。 -->
    <meta name="format-detection" content="telephone=no">
    <!-- 禁用默认行为，允许样式立即响应。 -->
    <meta name="mobile-web-app-capable" content="yes">

    <style>
        body {
            padding: 0;
            margin: 0;
        }

        #loading-container {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .loading-txt {
            font-size: 14px;
            margin-top: -30px;
            color: #2662FF;
            font-family: PingFang SC, Microsoft YaHei UI, Microsoft YaHei, Source Han Sans CN, sans-serif, Helvetica Neue, Helvetica, Arial;
        }
    </style>

    <!-- Set uiColorScheme cookie before any other scripts -->
    <script>
        // Helper to get a cookie by name
        function getCookie(name) {
            const value = `; ${document.cookie}`
            const parts = value.split(`; ${name}=`)
            if (parts.length === 2) return parts.pop().split(';').shift()
        }

        // Helper to set a cookie
        function setCookie(name, value, options = {}) {
            let cookieStr = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`
            if (options.maxAge) cookieStr += `; max-age=${options.maxAge}`
            if (options.path) cookieStr += `; path=${options.path}`
            document.cookie = cookieStr
        }

        // Call API to get UI version
        function callApi() {
            return fetch('/api/site/sysConfig', {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Skip-Encrypt': '1',
                },
            })
                .then(res => res.json())
                .then(data => {
                    sessionStorage.setItem('siteId', data.data?.siteId)
                    return { uiColorScheme: data.data && data.data.uiColorScheme }
                })
                .catch(() => ({ uiColorScheme: null }))
        }

        // Run the version check immediately
        (function () {
            // const alreadyRedirected = localStorage.getItem('uiReloaded');
            const uiColorScheme = getCookie('uiColorScheme')
            callApi().then((res) => {
                if (res.uiColorScheme && uiColorScheme !== res.uiColorScheme.toString()) {
                    setCookie('uiColorScheme', res.uiColorScheme, { path: '/', maxAge: 86400 })
                    // if (!alreadyRedirected) {
                    setTimeout(() => {
                        // localStorage.setItem('uiReloaded', '1');
                        window.location.replace(`${window.location.pathname}`)
                    }, 100)
                    // }
                }
            })
        })()
    </script>
</head>

<body>
<div id="app">
    <div id="loading-container">
        <img
                src="/images/loading2.gif"
                alt="loading"
        >
        <span class="loading-txt">正在加载中...</span>
    </div>
</div>
<script type="module" src="/src/main.js"></script>
<script>
    const t = Math.floor(Date.now() / 60000)
    const script = document.createElement('script')
    script.src = `https://cstaticdun.126.net/load.min.js?t=${t}`
    document.head.appendChild(script)
</script>
</body>
</html>
