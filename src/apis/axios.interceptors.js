import axios from 'axios'

import i18n, { localeStorage } from '@/i18n/index.js'
import { utils_decrypt } from '@/utils/index.js'

export const $token = useSessionStorage('token', '')

const isSkipEncrypt = useLocalStorage('devMode', false)

const service = axios.create({
    baseURL: '/api',
})

// 用于存储当前请求的取消令牌
export const cancelTokens = {}

// 请求错误
const onErr = async (err) => {
    // 未登录
    if (err.status === 401) {
        logout()
        await showDialog({
            title: i18n.global.t('auth.re_login'),
        })
    } else if (axios.isCancel(err)) {
        console.log('请求被取消', err.message)
    } else {
        await Promise.reject(err)
    }
}

// 取消器的唯一 Key
export const tokenKey = ({ url, method, params, headers }) => {
    const base = `${url}:${method}`

    return headers?.keepLatest === 'true' ? base : `${base}: ${JSON.stringify(toRaw(params))}`
}

// 请求拦截
service.interceptors.request.use(config => {
    const cancelTokenKey = tokenKey(config),
        { cancellable } = config.headers

    // 如果已经有这个请求的取消令牌，则取消之前的请求
    if (cancelTokens[cancelTokenKey] && cancellable !== false) {
        cancelTokens[cancelTokenKey].cancel(`Duplicate request cancelled:${cancelTokenKey}`)
    }

    // 创建一个新的取消令牌
    const cancelToken = axios.CancelToken.source()
    config.cancelToken = cancelToken.token

    // 保存取消令牌
    cancelTokens[cancelTokenKey] = cancelToken

    // 有 token 设置 token
    if ($token.value) {
        config.headers.Authorization = $token.value
    }

    // 多语言
    config.headers['accept-language'] = localeStorage.value

    // 接口响应不加密
    if (isSkipEncrypt.value || import.meta.env.MODE === 'development') config.headers['X-Skip-Encrypt'] = 1

    return config
}, onErr)

// 响应拦截
service.interceptors.response.use((response) => {
    // 取消器
    const cancelTokenKey = tokenKey(response.config)

    // 响应成功，移除该请求的取消令牌
    delete cancelTokens[cancelTokenKey]

    // 登录成功后会返回token
    const _token = response.headers.authorization
    if (_token) $token.value = _token

    const { code, data, msg } = utils_decrypt(response.data)

    if (code === 0) {
        return data
    } else {
        showFailToast(msg)
        console.error(`[url] => ${response.config.url} [code:${code}] => ${msg}`)
        return Promise.reject(response)
    }
}, onErr)

export default service
