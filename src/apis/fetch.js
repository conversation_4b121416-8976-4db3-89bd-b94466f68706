import _ from 'lodash'

import axios from './axios.interceptors.js'

// 请求方式
export const FETCH_METHOD = {
    POST: 'post',
    GET: 'get',
    DELETE: 'delete',
    PUT: 'put',
}

// 通用api请求
export function api_fetch({ url, params, method = 'post', options }) {
    try {
        switch (method) {
            case FETCH_METHOD.GET:
                return axios.get(
                    url,
                    {
                        params,
                        ...options,
                    },
                )
            case FETCH_METHOD.POST:
                return axios.post(
                    url,
                    params,
                    options,
                )
            case FETCH_METHOD.PUT:
                return axios.put(
                    url,
                    params,
                    options,
                )
            case FETCH_METHOD.DELETE:
                return axios.delete(
                    url,
                    {
                        params,
                        options,
                    },
                )
        }
    } catch (e) {
        return Promise.reject(e)
    }
}

export const api_post = arg => api_fetch({ ...arg, method: FETCH_METHOD.POST })
export const api_get = arg => api_fetch({ ...arg, method: FETCH_METHOD.GET })
export const api_put = arg => api_fetch({ ...arg, method: FETCH_METHOD.PUT })
export const api_delete = arg => api_fetch({ ...arg, method: FETCH_METHOD.DELETE })

/**
 * @function useRequest
 * @description 获取数据的请求封装
 * @param url {string} 请求接口地址
 * @param [params] {any} 请求参数
 * @param [method] {string} 请求方式
 * @param [initialValues] {any} 响应数据的默认值
 * @param [manual] {boolean} 是否手动请求：默认会自动请求，关闭的情况下需要手动触发
 * @param [formatResult] {function} 用于对响应数据进行处理的工具函数
 * @param [onSuccess] {function} 响应成功回调函数
 * @param [onErr] {function} 响应异常回调函数
 * @param [cancellable] {boolean} 是否可以被取消
 * @param [sessionKey] {string} sessionStorage 缓存 key
 * @param [localKey] {string} localStorage 缓存 key
 * @param [needLogin] {boolean} 接口是否需要判断登录状态
 * @param [interval] {number} 轮询秒数
 * */
export function useRequest({
    url,
    params,
    method,
    initialValues,
    manual,
    formatResult,
    onSuccess,
    onErr,
    cancellable,
    sessionKey,
    localKey,
    needLogin,
    interval,
}) {
    const { $isLogin } = storeToRefs(useProfileStore())

    const disabled = computed(() => needLogin && !$isLogin.value)

    const originalRes = ref()

    // 响应数据
    let res
    if (sessionKey) {
        res = useSessionStorage(sessionKey, initialValues)
    } else if (localKey) {
        res = useLocalStorage(localKey, initialValues)
    } else {
        res = ref(initialValues)
    }

    // 请求是否已经初始化 - 主要提供给骨架屏使用
    const initial = ref(false),
        // 加载状态
        loading = ref(false),
        refreshLoading = ref(false)

    // 取消器
    const controller = new AbortController(),
        signal = controller.signal

    /**
     * @function run
     * @description 手动触发请求函数
     * @param [runParams] {any} 请求时携带的参数
     * @return Promise<void>
     * */
    const run = async (runParams) => {
        if (disabled.value) return

        if (!loading.value) {
            loading.value = true

            // 判断实际请求的参数
            const actualParams = { ...unref(params), ...runParams }
            try {
                const response = await api_fetch({
                    url,
                    params: actualParams,
                    method: method ?? FETCH_METHOD.GET,
                    options: {
                        headers: {
                            cancellable,
                        },
                        signal,
                    },
                })

                originalRes.value = response

                res.value = formatResult ? formatResult(response) : response

                onSuccess?.(res.value, actualParams)

                if (!initial.value) initial.value = true
            } catch (e) {
                onErr?.(e)
            } finally {
                loading.value = false
                if (refreshLoading.value) refreshLoading.value = false
            }
        }
    }

    if (!manual && !disabled.value) run()

    const onRefresh = async () => {
        if (!disabled.value) await run()
    }

    let intervalInstance
    if (interval) {
        intervalInstance = setInterval(async () => {
            res.value = await api_fetch({
                url,
                params: unref(params),
                method: method ?? FETCH_METHOD.GET,
            })
        }, interval * 1000)
    }

    tryOnBeforeUnmount(() => {
        if (cancellable !== false) {
            controller.abort()
            clearInterval(intervalInstance)
            intervalInstance = null
        }
    })

    return {
        controller,
        initial,
        loading,
        refreshLoading,
        originalRes,
        res,
        run,
        onRefresh,
    }
}

// 默认响应的分页参数名
const defaultResponseKeys = {
    list: 'records',
    current: 'current',
    pageSize: 'pageSize',
    total: 'total',
}

// 默认的请求传入分页参数名
const defaultPaginationKeys = {
    current: 'pageNumber',
    pageSize: 'pageSize',
    total: 'total',
}

/**
 * @function usePagination
 * @description 对useRequest的二次封装：主要用于分页请求的场景
 * @param fetchOptions {object} useRequest的参数配置
 * @param [config] {object} 配置
 * @param [config.responseKeys] {object} 响应参数名配置
 * @param [config.paginationKeys] {object} 请求分页参数名配置
 * @param [config.paginationOptions] {object} 默认分页参数配置
 * */
export function usePagination(
    fetchOptions,
    config,
) {
    const responseKeys = {
        ...defaultResponseKeys,
        ...config?.responseKeys,
    }

    const paginationKeys = {
        ...defaultPaginationKeys,
        ...config?.paginationKeys,
    }

    const {
        paginationOptions,
    } = config ?? {}

    const pagination = ref({
        current: 1,
        pageSize: 20,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        ...paginationOptions,
    })

    const refreshLoading = ref(false),
        loadLoading = ref(false)

    // 请求返回的列表数据
    const list = ref([]),
        // 是否请求到最后一页
        finished = ref(false)

    const filterPrams = ref({})

    // 请求参数
    const requestParams = computed(() => {
        let baseFetchParams = {
            [paginationKeys.current]: pagination.value.current,
            [paginationKeys.pageSize]: pagination.value.pageSize,
        }
        if (fetchOptions?.params) {
            const optionParams = unref(fetchOptions.params)
            baseFetchParams = {
                ...optionParams,
                ...baseFetchParams,
                ...filterPrams.value,
            }
        }

        return baseFetchParams
    })

    // 调用 useRequest
    const { controller, initial, loading, res, run } = useRequest({
        ...fetchOptions,
        params: requestParams,
        formatResult: res => {
            if (fetchOptions.formatResult) {
                return {
                    ...res,
                    [responseKeys.list]: fetchOptions.formatResult(res[responseKeys.list]),
                }
            }
            return res
        },
        onSuccess: res => {
            try {
                const dataSource = res[responseKeys.list],
                    resTotal = res[responseKeys.total]

                // 初始请求：赋值到列表数据
                if (pagination.value.current === 1) {
                    list.value = dataSource
                } else {
                    // 列表数据无限添加新数据
                    list.value = [
                        ...list.value,
                        ...dataSource,
                    ]
                }

                pagination.value = {
                    ...pagination.value,
                    total: resTotal,
                }

                // 判断是否为最后一页
                if (!dataSource.length) finished.value = true
                else if ('hasNext' in res) finished.value = !res.hasNext
                else finished.value = list.value.length >= resTotal
            } finally {
                fetchOptions.onSuccess?.(list.value, requestParams.value, res)
            }
        },
        onErr: err => {
            finished.value = true
            fetchOptions.onErr?.(err)
        },
    })

    // 刷新
    const onRefresh = async () => {
        pagination.value.current = 1
        try {
            await run(requestParams.value)
        } finally {
            refreshLoading.value = false
        }
    }

    // 加载下一页：用于移动端
    const onLoadMore = async () => {
        try {
            if (!finished.value) {
                pagination.value.current += 1
                await run(requestParams.value)
            }
        } finally {
            loadLoading.value = false
        }
    }

    // 分页请求
    const onChange = async (_pagination) => {
        if (_pagination) {
            _.entries(paginationKeys).forEach(([ antKey, paramKey ]) => {
                pagination.value[paramKey] = _pagination[antKey]
            })
        }
        await run(requestParams.value)
    }

    // 普通请求
    const _run = async (runParams) => {
        pagination.value.current = 1
        await run({
            ...requestParams.value,
            ...unref(runParams),
        })
    }

    const onFilter = async ({ dataIndex, sort }) => {
        if (sort === SORT_CONFIG.NORMAL) {
            filterPrams.value = {}
        } else {
            filterPrams.value = {
                field: dataIndex,
                order: sort,
            }
        }
        refreshLoading.value = true
        await onRefresh()
    }
    return {
        list,
        controller,
        initial,
        loading,
        refreshLoading,
        loadLoading,
        res,
        pagination,
        finished,
        run: _run,
        onFilter,
        onRefresh,
        onLoadMore,
        onChange,
    }
}
