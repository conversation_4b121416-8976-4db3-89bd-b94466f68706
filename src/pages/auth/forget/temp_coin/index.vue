<template>
    <AuthPopup class="pb-6">
        <div
            class="mb-8 ml-[13px] text-md relative flex items-center text-white justify-center w-[140px] h-[40px] skew-btn"
            style="--skew-bg-color: var(--primary)"
        >
            {{ t('_forget') }}
        </div>

        <van-form @submit="onValid">
            <c-input
                required
                type="tel"
                inputmode="numeric"
                :maxlength="11"
                :placeholder="t('form.input_placeholder', [ t('profile.mobile') ])"
                v-model="formState.mobile"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="mobile"/>
                </template>
            </c-input>

            <c-input
                class="flex-1"
                inputmode="numeric"
                :maxlength="6"
                :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                v-model="formState.smsCode"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="captcha"/>
                </template>

                <template #addOnAfter>
                    <van-button
                        type="primary"
                        :loading="otpLoading"
                        :disabled="!formState.mobile || otpDisabled"
                        @click="onSendOtp(formState.mobile)"
                    >
                        {{ otpText }}
                    </van-button>
                </template>
            </c-input>

            <c-input
                autocomplete="new-password"
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.password_regex')"
                v-model="formState.password"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <c-input
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.repeat')"
                v-model="formState.repeat"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <div class="mt-8">
                <van-button
                    type="primary"
                    native-type="submit"
                    block
                    round
                    :loading
                    :disabled
                >
                    {{ t('form.reset') }}
                </van-button>
            </div>
        </van-form>

        <div class="text-center mt-5 text-xs text-paragraph">
            <router-link
                class="text-link underline"
                :to="{ name: 'login' }"
                replace
            >
                {{ t('_login') }}
            </router-link>
        </div>
    </AuthPopup>
</template>

<script setup>
import { useForget } from '@/pages/auth/composables/useForget.js'

const {
    formState,
    t,
    onSubmit,
    loading,
    onValid,
    disabled,
    otpText,
    otpLoading,
    otpDisabled,
    onSendOtp,
    AuthPopup,
} = useForget()

defineOptions({ name: 'forget' })
</script>

<style scoped>
.c-controller:not(:last-child) {
    margin-bottom: 20px;
}
</style>
