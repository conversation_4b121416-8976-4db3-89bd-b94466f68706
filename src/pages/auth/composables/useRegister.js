import AuthPopup from '@/layout/auth/components/_TEMPLATE_/AuthPopup.vue'

export const useRegister = () => {

    const { replace } = useRouter()

    const { $inviteCode } = storeToRefs(useGlobalStore())

    // 获取是否开启登录时网易验证开关
    const { res: isNeedAuth, onRefresh: onRefreshAuth } = useRequest({
        url: '/yidun/getEnable', initialValues: false, params: {
            sceneType: 2,
        },
    })

    const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({
        type: OTP_TYPES.REGISTER,
    }, computed(() => isNeedAuth.value))

    const formState = ref({
        mobile: '', smsCode: '', password: '', repeat: '', inviteCode: $inviteCode.value,
    })

    const disabled = useFormDisabled(formState, [ 'inviteCode' ])

    const onValid = () => {
        const { mobile, password, repeat, smsCode } = formState.value

        if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))

        if (!REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))
        if (!REGULAR.NEW_PASSWORD.test(repeat)) return showFailToast(t('auth.password_error'))

        if (password !== repeat) return showFailToast(t('auth.repeat_error'))

        if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

        onSubmit()
    }

    const [ onSubmit, loading ] = useFetchLoading(async () => {
        const { mobile, password, smsCode, inviteCode } = formState.value

        await api_post({
            url: '/auth/register', params: {
                mobile,
                inviteCode,
                password: btoa(password),
                smsCode: smsCode,
            },
        })

        localStorage.setItem('loginInfo', JSON.stringify({ mobile, password, smsCode: '' }))

        showSuccessToast(t('_successfully'))
        await replace({ name: 'login' })
    })

    const { t } = useI18n({
        useScope: 'global', messages: {
            [LANGUAGE.zhCN]: {
                _register: '立即注册',
                _register_welcome: '欢迎注册',
                _successfully: '注册成功',
                _has: '已有账号？',
                _login: '立即登录',
            }, [LANGUAGE.zhHK]: {
                _register: '立即注册',
                _register_welcome: '欢迎注册',
                _successfully: '注册成功',
                _has: '已有账号？',
                _login: '立即登录',
            }, [LANGUAGE.enUS]: {
                _register: 'Register',
                _register_welcome: 'Welcome to Register',
                _successfully: 'Register Successfully',
                _has: 'Had an account?',
                _login: 'To Login',
            },
        },
    })

    return {
        AuthPopup,
        t,
        onSubmit,
        loading,
        formState,
        otpText,
        otpLoading,
        otpDisabled,
        onSendOtp,
        disabled,
        onValid,
        $inviteCode,
    }
}
