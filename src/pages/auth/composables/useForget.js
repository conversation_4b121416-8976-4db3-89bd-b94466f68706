import AuthPopup from '@/layout/auth/components/_TEMPLATE_/AuthPopup.vue'
import { API_PATH } from '@/apis/index.js'

export const useForget = () => {

    const { replace } = useRouter()

    const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.UPDATE_PASSWORD })

    const formState = ref({
        mobile: '',
        smsCode: '',
        password: '',
        repeat: '',
    })

    const disabled = useFormDisabled(formState)

    const onValid = () => {
        const { mobile, password, smsCode, repeat } = formState.value

        if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))
        if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

        if (!REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))
        if (!REGULAR.NEW_PASSWORD.test(repeat)) return showFailToast(t('auth.password_error'))

        if (password !== repeat) return showFailToast(t('auth.repeat_error'))

        onSubmit()
    }

    const [ onSubmit, loading ] = useFetchLoading(async () => {
        const { mobile, password, smsCode } = formState.value

        await api_post({
            url: API_PATH.UPDATE_PASSWORD,
            params: {
                mobile,
                newPassword: btoa(password),
                smsCode,
                verifyType: 'mobile',
                type: 1,
            },
        })

        sessionStorage.setItem('loginInfo', JSON.stringify({ mobile, password, smsCode: '' }))

        showSuccessToast(t('_successfully'))
        replace({ name: 'login' })
    })

    const { t } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _forget: '忘记密码',
                _login: '返回登录',
                _successfully: '修改密码成功',
            },
            [LANGUAGE.zhHK]: {
                _forget: '忘记密码',
                _login: '返回登录',
                _successfully: '修改密码成功',
            },
            [LANGUAGE.enUS]: {
                _forget: 'Forgot Password',
                _login: 'To Login',
                _successfully: 'Update Password Successfully',
            },
        },
    })
    return {
        formState,
        t,
        onSubmit,
        loading,
        onValid,
        disabled,
        otpText,
        otpLoading,
        otpDisabled,
        onSendOtp,
        AuthPopup,
    }
}
