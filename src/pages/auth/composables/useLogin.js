import { OTP_VERIFY } from '@/hooks'
import socket from '@/socket.js'
import AuthPopup from '@/layout/auth/components/_TEMPLATE_/AuthPopup.vue'
import Input from '@/components/Controller/Input.vue'
import Icon from '@/components/Icon/index.vue'

export const useLogin = () => {
    const { replace } = useRouter(),
        { dispatch_refreshProfile } = useProfileStore(),
        { dispatch_refreshAccount } = useAccountStore(),
        { dispatch_refreshUnreadCount } = useMessageStore()

    // 获取是否开启登录时网易验证开关
    const { res: isNeedAuth, onRefresh: onRefreshSmsAuth } = useRequest({
        url: '/yidun/getEnable', initialValues: false, params: {
            sceneType: 2,
        },
    })


    const {
        otpText,
        otpLoading,
        otpDisabled,
        onSendOtp,
    } = useOtp({ type: OTP_TYPES.LOGIN }, computed(() => isNeedAuth.value))

    const loginMode = ref(OTP_VERIFY?.ACCOUNT),
        isPasswordMode = computed(() => loginMode.value === OTP_VERIFY.ACCOUNT),
        remember = ref(true),
        protocol = useSessionStorage('loginProtocol', true)

    const { onShowProtocol: onShowServiceProtocol, ProtocolPopup: ServiceProtocol } = useProtocolPopup(2),
        { onShowProtocol: onShowPrivacyProtocol, ProtocolPopup: PrivacyProtocol } = useProtocolPopup(3)

    const formState = useLocalStorage('loginInfo', {
        mobile: '',
        password: '',
        smsCode: '',
    })

    const disabled = computed(() => {
        const { mobile, password, smsCode } = formState.value

        return !mobile || !(isPasswordMode.value ? password : smsCode)
    })

    const mobileFormItem = () => h(
        Input,
        {
            required: true,
            maxlength: 11,
            placeholder: t('form.input_placeholder', [ t('profile.mobile') ]),
            inputmode: 'numeric',
            modelValue: formState.value.mobile,
            'onUpdate:modelValue': val => {
                formState.value.mobile = val
            },
        },
        {
            prefix: () => h(
                Icon,
                {
                    prefix: 'auth',
                    name: 'mobile',
                },
            ),
        },
    )

    // 获取是否开启登录时网易验证开关
    const { res: isLoginEnableAuth, onRefresh: onRefreshAuth } = useRequest({
        url: '/yidun/getEnable',
        initialValues: false,
        params: {
            sceneType: 1,
        },
    })

    const { onAwaitCaptcha, validate } = useCaptcha()

    const onValid = () => {
        const { mobile, password, smsCode } = formState.value
        if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))

        if (isPasswordMode.value && !REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))

        if (!isPasswordMode.value && !REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

        onSubmit()
    }

    const [ onSubmit, loading ] = useFetchLoading(async () => {
        // 开启网易验证
        if (isLoginEnableAuth.value) await onAwaitCaptcha()

        const els = document.querySelectorAll('.yidun_popup--light.yidun_popup.yidun_popup--size-small.yidun_popup--append')
        if (els?.length) {
            els?.forEach(el => {
                el.style.display = 'none'
            })
        }

        const params = {
            mobile: formState.value.mobile,
            password: btoa(formState.value.password),
            smsCode: formState.value.smsCode,
            verifyType: loginMode.value,
            validate: isLoginEnableAuth.value ? validate.value : undefined,
            sceneType: 1,
        }

        try {
            await api_post({
                url: '/auth/login',
                params,
            })

            if (!remember.value) {
                formState.value = {
                    mobile: '',
                    password: '',
                    smsCode: '',
                }
            } else {
                formState.value.smsCode = ''
            }

            showSuccessToast(t('_successfully'))

            socket.emit({
                type: 'auth',
                data: $token.value,
            })

            await Promise.all([
                replace('/home'),
                dispatch_refreshProfile(),
                dispatch_refreshAccount(),
                dispatch_refreshUnreadCount(),
            ])
        } catch (e) {
            await onRefreshAuth()
            await onRefreshSmsAuth()
        }
    })

    const { t } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _account_login: '密码登录',
                _mobile_login: '验证码登录',
                _remember: '记住密码',
                _protocol: '点击登录即代表同意{0}和{1}',
                _protocol_service: '《服务条款》',
                _protocol_privacy: '《隐私政策》',
                _register_tip: '还没有账号?{0}',
                _register_now: '马上开户',
                _successfully: '登录成功',
            },
            [LANGUAGE.zhHK]: {
                _account_login: '密码登录',
                _mobile_login: '验证码登录',
                _remember: '记住密码',
                _protocol: '点击登录即代表同意{0}和{1}',
                _protocol_service: '《服务条款》',
                _protocol_privacy: '《隐私政策》',
                _register_tip: '还没有账号?{0}',
                _register_now: '马上开户',
                _successfully: '登录成功',
            },
            [LANGUAGE.enUS]: {
                _account_login: 'Password login',
                _mobile_login: 'OPT login',
                _remember: 'Remember Password',
                _protocol: 'Protocol {0} & {1}',
                _protocol_service: '《Terms of Service》',
                _protocol_privacy: '《Privacy Policy》',
                _register_tip: 'Don\'t have an account? {0}',
                _register_now: 'Register Now',
                _successfully: 'Login Successfull',
            },
        },
    })
    return {
        OTP_VERIFY,
        AuthPopup,
        onValid,
        loginMode,
        mobileFormItem,
        formState,
        t,
        otpLoading,
        otpDisabled,
        onSendOtp,
        otpText,
        isPasswordMode,
        remember,
        disabled,
        protocol,
        onShowServiceProtocol,
        onShowPrivacyProtocol,
        ServiceProtocol,
        PrivacyProtocol,
        loading,
    }
}
