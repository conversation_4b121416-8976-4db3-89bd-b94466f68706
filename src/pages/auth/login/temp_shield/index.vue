<template>
    <AuthPopup>
        <van-form @submit="onValid">
            <van-tabs
                shrink
                v-model:active="loginMode"
            >
                <!-- 密码登录 -->
                <van-tab
                    :title="t('_account_login')"
                    :name="OTP_VERIFY?.ACCOUNT"
                >
                    <div class="my-5">
                        <mobileFormItem/>

                        <c-input
                            type="password"
                            :required="isPasswordMode"
                            :maxlength="16"
                            :placeholder="t('form.input_placeholder', [ t('auth.password_account') ])"
                            v-model="formState.password"
                        >
                            <template #prefix>
                                <c-icon prefix="auth" name="lock"/>
                            </template>
                        </c-input>
                    </div>
                </van-tab>
                <!-- 密码登录 -->

                <!-- 短信验证码登录 -->
                <van-tab :title="t('_mobile_login')" :name="OTP_VERIFY.MOBILE">
                    <div class="my-5">
                        <mobileFormItem/>

                        <c-input
                            class="flex-1"
                            inputmode="numeric"
                            :required="!isPasswordMode"
                            :maxlength="6"
                            :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                            v-model="formState.smsCode"
                        >
                            <template #prefix>
                                <c-icon prefix="auth" name="captcha"/>
                            </template>

                            <template #addOnAfter>
                                <van-button
                                    type="primary"
                                    :loading="otpLoading"
                                    :disabled="!formState.mobile || otpDisabled"
                                    @click="onSendOtp(formState.mobile)"
                                >
                                    {{ otpText }}
                                </van-button>
                            </template>
                        </c-input>
                    </div>
                </van-tab>
                <!-- 短信验证码登录 -->
            </van-tabs>
            <div>
                <!-- 记住密码 -->
                <div class="flex-middle mb-5 text-sm">
                    <van-checkbox
                        v-if="isPasswordMode"
                        shape="square"
                        icon-size="14"
                        v-model="remember"
                    >
                        {{ t('_remember') }}
                    </van-checkbox>

                    <router-link
                        class="ml-auto text-link"
                        :to="{ name: 'forget' }"
                        replace
                    >
                        {{ t('auth.forget') }}?
                    </router-link>
                </div>
                <!-- 记住密码 -->
                <!-- 提交按钮 -->
                <van-button
                    type="primary"
                    native-type="submit"
                    block
                    round
                    :loading
                    :disabled="disabled || !protocol"
                >
                    {{ t('auth.login') }}
                </van-button>
                <!-- 提交按钮 -->

                <!-- 协议 -->
                <div
                    data-aos="fade-left"
                    data-aos-delay="150"
                    data-aos-anchor="#app"
                    class="text-xs text-text1 mt-3 flex-middle"
                >
                    <van-checkbox
                        class="mr-2"
                        :icon-size="14"
                        v-model="protocol"
                    />

                    <i18n-t scope="global" keypath="_protocol">
                        <span class="text-link" @click="onShowServiceProtocol">
                            {{ t('_protocol_service') }}
                        </span>
                        <span class="text-link" @click="onShowPrivacyProtocol">
                            {{ t('_protocol_privacy') }}
                        </span>
                    </i18n-t>
                </div>
                <!-- 协议 -->

                <!-- 跳转注册 -->
                <i18n-t
                    class="text-center mt-5 text-text text-xs"
                    tag="div"
                    keypath="_register_tip"
                    scope="global"
                >
                    <router-link
                        replace
                        to="/auth/register"
                    >
                        <span class="ml-1 text-link underline">{{ t('_register_now') }}</span>
                    </router-link>
                </i18n-t>
            </div>
        </van-form>
    </AuthPopup>

    <ServiceProtocol/>
    <PrivacyProtocol/>
</template>

<script setup>
import { useLogin } from '@/pages/auth/composables/useLogin.js'

const {
    OTP_VERIFY,
    AuthPopup,
    onValid,
    loginMode,
    mobileFormItem,
    formState,
    t,
    otpLoading,
    otpDisabled,
    onSendOtp,
    otpText,
    isPasswordMode,
    remember,
    disabled,
    protocol,
    onShowServiceProtocol,
    onShowPrivacyProtocol,
    ServiceProtocol,
    PrivacyProtocol,
    loading,
} = useLogin()

defineOptions({ name: 'login' })
</script>

<style scoped>
.c-controller:not(:last-child) {
    margin-bottom: 20px;
}
</style>
