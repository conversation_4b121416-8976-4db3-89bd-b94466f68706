<template>
    <div class="home with-header-container">
        <!-- 轮播图 -->
        <van-skeleton :loading="!carouselInitial">
            <template #template>
                <van-skeleton-image data-aos="fade-left" class="w-full! h-[126px]! mt-3 mb-4"/>
            </template>

            <van-swipe class="mt-1" data-aos="fade-left">
                <van-swipe-item
                    v-for="({ id, title, imageUrl, jumpType, jumpUrl }) in carousel"
                    :key="id"
                    @click="utils_jump(jumpType, jumpUrl)"
                >
                    <van-image
                        class="w-full h-[126px] rounded-lg overflow-hidden"
                        :src="imageUrl"
                        :alt="title"
                    />
                </van-swipe-item>
                <template #indicator="{ active, total }">
                    <div class="custom-carousel-dots flex items-center justify-center gap-1.5 my-2">
                        <span
                            v-for="n in total"
                            :key="n"
                            :class="[
                                'inline-block rounded-full transition-all duration-200',
                                n - 1 === active
                                    ? 'w-6 h-2 bg-primary'
                                    : 'w-2 h-2 bg-primary/40'
                            ]"
                        ></span>
                    </div>
                </template>
            </van-swipe>
        </van-skeleton>
        <!-- 轮播图 -->

        <div class="flex justify-evenly items-center gap-5 overflow-hidden rounded-lg py-2.5">
            <div
                data-aos="fade-left"
                v-for="({ title, icon, to, handler }, index) in nav"
                @click="handler ? handler() : $router.push(to)"
                :key="index"
                class="flex-1 flex flex-col rounded items-center justify-center"
            >
                <c-icon :name="icon" size="32" color="var(--primary)"/>
                <span class="text-sm text-regular whitespace-nowrap mt-1.25"> {{ title }}</span>
            </div>
        </div>

        <!-- 滚动公告 -->
        <van-notice-bar
            ref="marqueeRef"
            data-aos="fade-left"
            data-aos-delay="50"
            class="mt-1 rounded-md"
            scrollable
            background="var(--bg)"
            color="var(--text)"
            left-icon="volume-o"
        >
            <div class="flex-middle gap-4">
                <div
                    class="flex"
                    v-for="({ id, content }) in $notice.marquee"
                    :key="id"
                    v-html="content"
                />
            </div>
            <template #left-icon>
                <c-icon name="notice" size="25" color="var(--text)"/>
            </template>
        </van-notice-bar>
        <!-- 滚动公告 -->
        <div class="bg-bg4 mt-2.5 px-2.5 rounded-lg">
            <van-tabs
                v-model:active="stockTabActive2"
                class="text-sm text-title h-13 items-center"
                data-aos="fade-left"
                data-aos-delay="100"
                shrink
                :ellipsis="false"
                style="--van-tab-font-size: 14px;"
            >
                <van-tab
                    v-if="showIndexSection"
                    :title="t('stock.index')"
                    :name="0"
                >
                <template #title>
                    <span :class="stockTabActive2 === 0 ? 'activeTav' : ''">
                        {{ t('stock.index') }}
                    </span>
                </template>
                </van-tab>
                <van-tab
                    v-if="showIndexSection"
                    :title="t('stock.title')"
                    :name="1"
                >
                <template #title>
                    <span :class="stockTabActive2 === 1 ? 'activeTav' : ''">
                        {{ t('stock.title') }}
                    </span>
                </template>
                </van-tab>
                <van-tab
                    v-if="showFuturesSection"
                    :title="t('stock.futures')"
                    :name="2"
                >
                <template #title>
                    <span :class="stockTabActive2 === 2 ? 'activeTav' : ''">
                        {{ t('stock.futures') }}
                    </span>
                </template>
                </van-tab>
                <van-tab
                    :title="t('stock.collect')"
                    :name="3"
                >
                <template #title>
                    <span :class="stockTabActive2 === 3 ? 'activeTav' : ''">
                        {{ t('stock.collect') }}
                    </span>
                </template>
                </van-tab>
            </van-tabs>

            <!--股指-->
            <IndexTrend
                v-if="stockTabActive2 === 0 && showIndexSection"
                class="mt-2.5"
                @select="onRedirectIndexDetails"
            />
            <van-tabs
                v-if="stockTabActive2 === 1 && showIndexSection"
                shrink
                v-model:active="$marketType"
                style="--van-tab-font-size: 12px;"
            >
                <template #nav-right>
                    <router-link to="/quotes/stock" class="text-xs ml-auto">
                        <span>{{ t('common.more') }}</span>
                        <van-icon name="arrow"/>
                    </router-link>
                </template>

                <van-tab
                    class="pt-2.5"
                    v-for="(_, symbol) in STOCK_CONFIG"
                    :key="symbol"
                    :name="symbol"
                    :title="t(`stock.${symbol}`)"
                >
                    <StockHot :market="symbol"/>
                </van-tab>
            </van-tabs>
            <!-- 期货 -->
            <FuturesList v-if="stockTabActive2 === 2 && showFuturesSection"/>

            <!--自选-->
            <c-table
                v-if="stockTabActive2 === 3"
                data-aos="fade-left"
                :columns
                :onRefresh="onRefreshCollect"
                :data-source="collectList"
                v-model:refresh-loading="collectRefreshLoading"
                @row-click="dispatch_checkStock"
                @filter="onFilterCollect"
            />
        </div>
        <!--证券-->

        <!--新闻-->
        <div class="bg-bg4 px-2.5 mt-2.5 rounded-lg">
            <div class="bg-bg4 py-2.5">
                <span class="activeTav" @click="stockTabActive2 = 3">{{
                        t('header.news')
                    }}</span>
            </div>
            <NewsList/>
        </div>
        <!-- 新闻 -->

        <!--浮动按钮-->
        <van-floating-bubble
            class="download"
            axis="xy"
            magnetic="x"
            v-model:offset="offset"
            :gap="{ x: 20, y: 60 }"
            @click="onDownload"
            teleport="body"
        >
            <div>APP</div>
            <div>下载</div>
        </van-floating-bubble>
    </div>
</template>

<script setup>
import { useHome } from '@/pages/main/home/<USER>/useHome.js'
import IndexTrend from '@/pages/main/quotes/routes/index/components/Index_Trend.Bull.vue'
import { $theme } from '@/store/index.js'
import bgImageLight from '/skins/templates/_TEMPLATE_/_THEME_/bg_light.png'
import bgImageDark from '/skins/templates/_TEMPLATE_/_THEME_/bg_dark.png'

const deviceW = window.screen.width
const offset = ref({ x: deviceW - 60, y: 260 })
const nav = computed(() => [
    {
        title: t('header.about'), icon: 'about_us', to: '/about',
    },
    {
        title: t('_ai'), icon: 'ai', to: '/ai',
    },
    {
        title: t('_vip'), icon: 'vip', to: '/vip',
    },
    {
        title: t('header.mission'), icon: 'mission', to: '/mission',
    },
    {
        title: t('header.service'), icon: 'customer', handler: () => {
            utils_link($globalConfig.value.service)
        },
    },
])

const {
    t,
    onRedirectIndexDetails,
    stockTabActive2,
    collectList,
    collectRefreshLoading,
    onFilterCollect,
    columns,
    onRefreshCollect,
    carousel,
    carouselInitial,
    onDownload,
    showIndexSection,
    showFuturesSection,
    sysConfigStore,
    $globalConfig,
    $marketType,
    dispatch_checkStock,
    $notice,
    utils_jump,
    STOCK_CONFIG,
    $activityActiveTab,
    NOTICE_TYPE,
    NewsList,
    StockHot,
    FuturesList,
} = useHome()

onMounted(() => {
    const appEl = document.querySelector('#app')
    appEl.style.background = `var(--page) url(${$theme.value === 'light' ? bgImageLight : bgImageDark}) no-repeat top center`
    appEl.style.backgroundSize = 'contain, auto'
    appEl.style.backgroundPositionY = '-54px'
})

onBeforeUnmount(() => {
    const appEl = document.querySelector('#app')
    if (appEl) {
        appEl.style.background = ''
        appEl.style.backgroundSize = ''
        appEl.style.backgroundPositionY = ''
    }
})

defineOptions({ name: 'Home' })
</script>

<style scoped>
.activeTav {
    font-weight: bold;
    color: #FFF;
    background-color: var(--primary);
    border-radius: 2px;
    padding: 3px 5px;
}
</style>