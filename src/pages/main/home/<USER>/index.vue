<template>
    <div class="home with-header-container">
        <div class="flex-between gap-5 overflow-hidden">
            <div
                class="btn flex gap-x-2.5 items-center rounded-lg px-3 h-[34px] text-white"
                data-aos="fade-left"
                v-for="({ title, icon, to, handler }, index) in nav"
                :data-aos-delay="`${index * 50}`"
                :key="index"
                @click="handler ? handler() : $router.push(to)"
            >
                <c-icon :name="icon" size="20"/>
                <div class="text-xs leading-8 whitespace-nowrap">
                    {{ title }}
                </div>
            </div>
        </div>

        <!-- 轮播图 -->
        <van-skeleton :loading="!carouselInitial">
            <template #template>
                <van-skeleton-image data-aos="fade-left" class="w-full! h-[126px]! mt-3 mb-4"/>
            </template>

            <van-swipe class="mt-4" data-aos="fade-left">
                <van-swipe-item
                    v-for="({ id, title, imageUrl, jumpType, jumpUrl }) in carousel"
                    :key="id"
                    @click="utils_jump(jumpType, jumpUrl)"
                >
                    <van-image
                        class="w-full h-[126px] rounded-lg overflow-hidden"
                        :src="imageUrl"
                        :alt="title"
                    />
                </van-swipe-item>
            </van-swipe>
        </van-skeleton>
        <!-- 轮播图 -->

        <!-- 滚动公告 -->
        <van-notice-bar
            ref="marqueeRef"
            data-aos="fade-left"
            data-aos-delay="50"
            class="mt-1 rounded-md"
            scrollable
            color="var(--text)"
            left-icon="volume-o"
        >
            <template #left-icon>
                <img src="./alarm.svg" alt="icon" class="w-6.26 h-6.25 ml-1"/>
            </template>

            <div class="flex-middle gap-4">
                <div
                    class="flex"
                    v-for="({ id, content }) in $notice.marquee"
                    :key="id"
                    v-html="content"
                />
            </div>
        </van-notice-bar>
        <!-- 滚动公告 -->

        <div
            class="text-sm flex gap-5 text-title h-13 items-center"
            data-aos="fade-left"
            data-aos-delay="100"
            shrink
        >
            <span v-if="showIndexSection" class="relative" :class="stockTabActive2 === 0 ? 'activeTav' : ''"
                  @click="stockTabActive2 = 0">{{
                    t('stock.index')
                }}</span>
            <span v-if="showIndexSection" class="relative" :class="stockTabActive2 === 1 ? 'activeTav' : ''"
                  @click="stockTabActive2 = 1">{{
                    t('stock.title')
                }}</span>
            <span v-if="showFuturesSection" class="relative" :class="stockTabActive2 === 2 ? 'activeTav' : ''"
                  @click="stockTabActive2 = 2">{{
                    t('stock.futures')
                }}</span>
            <span class="relative" :class="stockTabActive2 === 3 ? 'activeTav' : ''" @click="stockTabActive2 = 3">{{
                    t('stock.collect')
                }}</span>
        </div>

        <!--股指-->
        <IndexTrend
            v-if="stockTabActive2 === 0 && showIndexSection"
            class="mt-2.5"
            @select="onRedirectIndexDetails"
        />

        <!--证券-->
        <van-tabs
            v-if="stockTabActive2 === 1 && showIndexSection"
            shrink
            v-model:active="$marketType"
            style="--van-tab-font-size: 12px;"
        >
            <template #nav-right>
                <router-link to="/quotes/stock" class="text-xs ml-auto">
                    <span>{{ t('common.more') }}</span>
                    <van-icon name="arrow"/>
                </router-link>
            </template>

            <van-tab
                class="pt-2.5"
                v-for="(_, symbol) in STOCK_CONFIG"
                :key="symbol"
                :name="symbol"
                :title="t(`stock.${symbol}`)"
            >
                <StockHot :market="symbol"/>
            </van-tab>
        </van-tabs>

        <FuturesList v-if="stockTabActive2 === 2 && showFuturesSection"/>

        <!--自选-->
        <c-table
            v-if="stockTabActive2 === 3"
            data-aos="fade-left"
            :columns
            :onRefresh="onRefreshCollect"
            :data-source="collectList"
            v-model:refresh-loading="collectRefreshLoading"
            @row-click="dispatch_checkStock"
            @filter="onFilterCollect"
        />

        <!--新闻-->
        <van-tabs
            v-if="[0, 1, 3].includes(stockTabActive2)"
            class="h-full"
            data-aos="fade-left"
            data-aos-anchor="#app"
            shrink
            v-model:active="$activityActiveTab"
        >
            <!-- 新闻 -->
            <van-tab
                :title="t('header.news')"
                name="news"
                class="pt-2.5"
            >
                <NewsList/>
            </van-tab>
            <!-- 新闻 -->
        </van-tabs>

        <!--浮动按钮-->
        <van-floating-bubble
            class="download"
            axis="xy"
            magnetic="x"
            v-model:offset="offset"
            :gap="{ x: 20, y: 60 }"
            @click="onDownload"
        >
            <div>APP</div>
            <div>下载</div>
        </van-floating-bubble>
    </div>
</template>

<script setup>
import { useHome } from '@/pages/main/home/<USER>/useHome.js'

const deviceW = window.screen.width
const offset = ref({ x: deviceW - 60, y: 260 })
const nav = computed(() => [ {
    title: t('header.about'),
    icon: 'about_us',
    to: '/about',
},
    {
        title: t('_ai'),
        icon: 'ai',
        to: '/ai',
    },
    {
        title: t('header.service'),
        icon: 'customer',
        handler: () => {
            utils_link($globalConfig.value.service)
        },
    },
])
const {
    t,
    onRedirectIndexDetails,
    stockTabActive2,
    collectList,
    collectRefreshLoading,
    onFilterCollect,
    columns,
    onRefreshCollect,
    carousel,
    carouselInitial,
    onDownload,
    showIndexSection,
    showFuturesSection,
    sysConfigStore,
    $globalConfig,
    $marketType,
    dispatch_checkStock,
    $notice,
    IndexTrend,
    utils_jump,
    STOCK_CONFIG,
    $activityActiveTab,
    NOTICE_TYPE,
    NewsList,
    StockHot,
    FuturesList,
} = useHome()

defineOptions({ name: 'Home' })
</script>

<style scoped>
.activeTav {
    font-weight: bold;
    color: var(--active);
}

.activeTav::after {
    content: ' ';
    position: absolute;
    z-index: 1;
    width: var(--van-tabs-bottom-bar-width);
    height: var(--van-tabs-bottom-bar-height);
    background: var(--van-tabs-bottom-bar-color);
    border-radius: var(--van-tabs-bottom-bar-height);
    left: 50%;
    bottom: -12px;
    transform: translateX(-50%);
}

.btn {
    background: linear-gradient(90deg, #1890FF 0%, #2662FF 100%);
}
</style>
