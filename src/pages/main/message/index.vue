<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container__noPadding">
        <van-tabs
            class="h-full"
            data-aos="fade-left"
            v-model:active="messageActiveTab"
        >
            <!-- 系统消息 -->
            <van-tab
                :title="t('_system')"
                name="system"
                class="p-2.5"
            >
                <MessageRecord type="system"/>
            </van-tab>
            <!-- 系统消息 -->

            <!-- 公告 -->
            <!--<van-tab-->
            <!--    :title="t('_notice')"-->
            <!--    name="notice"-->
            <!--    class="p-2.5"-->
            <!--&gt;-->
            <!--    <NoticeList :type="NOTICE_TYPE.POPUP"/>-->
            <!--</van-tab>-->
            <!-- 公告 -->

            <!-- 股票预警 -->
            <van-tab
                :title="t('_warning')"
                name="warning"
                class="p-2.5"
            >
                <MessageRecord type="warning"/>
            </van-tab>
            <!-- 股票预警 -->
        </van-tabs>
    </div>
</template>

<script setup>
import MessageRecord from './components/MessageRecord.vue'
import { messageActiveTab } from '@/store/message.js'

const { dispatch_refreshUnreadCount } = useMessageStore()
dispatch_refreshUnreadCount()

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '消息中心',
            _system: '系统消息',
            _notice: '公告',
            _warning: '股票预警',
        },
        [LANGUAGE.zhHK]: {
            _title: '消息中心',
            _system: '系统消息',
            _notice: '公告',
            _warning: '股票预警',
        },
        [LANGUAGE.enUS]: {
            _title: 'Message',
            _system: 'System',
            _notice: 'Notice',
            _warning: 'Warning',
        },
    },
})

defineOptions({ name: 'message' })
</script>

<style scoped>
</style>
