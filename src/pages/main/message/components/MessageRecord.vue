<template>
    <c-record
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="mb-2.5"
            v-for="({ id, title, content, status, createTime }) in list"
            :key="id"
            :title
        >
            <template #extra>
                <van-badge v-if="!status" dot/>
            </template>

            <div class="text-xs whitespace-pre-line">
                {{ content }}
                <p>
                    {{ createTime }}
                </p>
            </div>

        </c-card>
    </c-record>
</template>

<script setup>
import { useMessageStore } from '@/store/index.js'

const { type } = defineProps({
    type: String,
    required: true,
})

const messageStore = useMessageStore()
const { $unreadCount } = storeToRefs(messageStore)

const {
    list,
    refreshLoading,
    loadLoading,
    finished,
    onRefresh,
    onLoadMore,
    run,
} = usePagination({
    url: '/message/page',
    params: {
        type,
    },
})

watch(() => $unreadCount.value, (nv, ov) => {
    if (nv !== ov) {
        run()
    }
})

defineOptions({ name: 'MessageRecord' })
</script>

<style scoped>

</style>
