<template>
    <c-header :title="t('_title')">
        <template #right>
            <van-icon
                name="replay"
                :class="{ 'animate-spin': loading }"
                @click="onRefresh"
            />
        </template>
    </c-header>

    <div class="with-header-container">
        <c-card
            class="mb-2.5 flex-middle gap-4 text-title"
            v-for="({ currency, icon, text }) in exchangeList"
            :key="currency"
        >
            <div class="flex items-center w-25">
                <c-icon size="32" :name="icon"/>
                <div class="flex flex-col ml-2.5">
                    <span class="text-sm">{{ text }}</span>
                    <span class="text-xs">{{ currency }}</span>
                </div>
            </div>

            <div class="flex-middle flex-1">
                <van-field
                    class="flex-1 border-b border-border"
                    clearable
                    type="number"
                    :min="0"
                    :max="100000000"
                    :model-value="principal[currency]"
                    @update:model-value="val => onFieldChange(currency, val)"
                    @focus="handleFocus(currency)"
                />
            </div>
        </c-card>
    </div>
</template>

<script setup>
import { utils_currency } from '@/utils/index.js'
import { debounce, isEqual } from 'lodash'

const rateStore = useRateStore()
const { dispatch_refreshRate } = rateStore
const { $rateConfig } = storeToRefs(rateStore)

const activeCurrency = ref('CNY')
const principal = ref({ CNY: 1, USD: 0, HKD: 0 })

const exchangeList = ref([
    { icon: 'CN', text: '人民币', currency: 'CNY' },
    { icon: 'US', text: '美元', currency: 'USD' },
    { icon: 'HK', text: '港币', currency: 'HKD' },
])

/** 获取当前汇率 */
const getRate = (key) => $rateConfig.value.find(v => v.text === key)?.rate || 0

/** 初始化 principal 值 */
const initPrincipal = () => {
    const rateUSD = getRate('USD')
    const rateHKD = getRate('HKD')

    principal.value = {
        CNY: 1,
        USD: utils_currency(1 * rateUSD, { precision: 4 }),
        HKD: utils_currency(1 * rateHKD, { precision: 4 }),
    }
}

/** 货币输入框聚焦事件 */
const handleFocus = (val) => {
    activeCurrency.value = val
}

/** 更新 principal 逻辑（避免死循环） */
function updatePrincipal(currency, val) {
    const rateUSD = getRate('USD')
    const rateHKD = getRate('HKD')

    const newPrincipal = { ...principal.value }

    if (currency === 'CNY') {
        newPrincipal.CNY = val
        newPrincipal.USD = utils_currency(val * rateUSD, { precision: 4 })
        newPrincipal.HKD = utils_currency(val * rateHKD, { precision: 4 })
    } else if (currency === 'USD') {
        newPrincipal.USD = val
        const cny = val / rateUSD
        newPrincipal.CNY = utils_currency(cny, { precision: 4 })
        newPrincipal.HKD = utils_currency(cny * rateHKD, { precision: 4 })
    } else if (currency === 'HKD') {
        newPrincipal.HKD = val
        const cny = val / rateHKD
        newPrincipal.CNY = utils_currency(cny, { precision: 4 })
        newPrincipal.USD = utils_currency(cny * rateUSD, { precision: 4 })
    }

    if (!isEqual(principal.value, newPrincipal)) {
        principal.value = newPrincipal
    }
}

/** 输入字段变化（加防抖） */
const onFieldChange = debounce((currency, val) => {
    if (activeCurrency.value !== currency) return
    updatePrincipal(currency, val)
}, 300)

/** 汇率变化时初始化 principal */
watch(() => $rateConfig.value, (val) => {
    if (val?.length) initPrincipal()
}, { immediate: true })

/** 重新拉取汇率 */
const [ onRefresh, loading ] = useFetchLoading(dispatch_refreshRate)

/** 多语言标题 */
const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: { _title: '汇率换算' },
        [LANGUAGE.zhHK]: { _title: '汇率換算' },
        [LANGUAGE.enUS]: { _title: 'Exchange rate conversion' },
    },
})

defineOptions({ name: 'exchange' })
</script>

