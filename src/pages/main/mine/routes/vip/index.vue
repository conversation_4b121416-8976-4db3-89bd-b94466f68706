<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container">
        <c-image-box
            :source="bg"
            :class="{ 'pt-2.5 gap-2.5': locale === isEn }"
            class="px-5 flex flex-col"
            container-class="min-h-[160px]"
        >
            <div class="flex-middle gap-4" :class="{ 'flex-1': locale !== !isEn }">
                <c-avatar :avatar="$profile.avatar"/>

                <div class="text-white">
                    {{ $profile.nickname }}
                </div>

                <c-icon
                    class="ml-auto"
                    prefix="mine"
                    size="40"
                    :name="`vip_${$profile.level}`"
                />
            </div>
            <div class="flex-1">
                <c-description-group
                    label-class="text-white"
                    :columns="isEn ? 1 : 2"
                    :items="privilegeItems"
                    :data-source="res"
                >
                    <template #template="{ value }">
                        <c-amount
                            class="text-white"
                            :amount="+value"
                            percent
                        />
                    </template>
                </c-description-group>
            </div>
        </c-image-box>

        <c-card class="mt-4" :title="t('_upgrade')">
            <c-description :label="t('_invite')">
                <c-amount
                    :suffix="t('_person')"
                    :precision="0"
                    :amount="next.remainInviteNumber"
                />
            </c-description>

            <van-progress
                class="my-4"
                color="var(--primary)"
                pivot-color="var(--title)"
                :stroke-width="6"
                :pivot-text="`${next.userInviteNumber}/${next.nextLevelInviteNumber}`"
                :percentage="_.clamp(next.userInviteNumber / (next.nextLevelInviteNumber || 1) * 100, 0, 100)"
            />

            <c-description :label="t('_deposit')">
                <c-amount
                    :currency="CURRENCY.CNY"
                    :amount="next.remainRechargeAmount"
                />
            </c-description>

            <van-progress
                class="my-4"
                color="var(--primary)"
                pivot-color="var(--title)"
                :stroke-width="6"
                :pivot-text="`${next.userRechargeAmount}/${next.nextLevelRechargeAmount}`"
                :percentage="_.clamp(next.userRechargeAmount / (next.nextLevelRechargeAmount || 1) * 100, 0, 100)"
            />
        </c-card>
    </div>
</template>

<script setup>
import _ from 'lodash'

import { CURRENCY } from '@/config'
import bg from '/skins/templates/_TEMPLATE_/_THEME_/bg_vip.png'

const { $profile } = storeToRefs(useProfileStore())

const privilegeItems = computed(() => [
    { label: t('_discount'), value: 'interestDiscount' },
    { label: t('vip.trade_rebate_ratio'), value: 'commissionRate' },
    { label: t('vip.interest_rebate_ratio'), value: 'interestRate' },
    { label: t('_rebate'), value: 'graduallyRate' },
])

const { res } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF%E4%BC%9A%E5%91%98/getUserLevelConfigUsingGET_1
    url: '/member/getUserLevelConfig',
    initialValues: {
        commissionRate: 0,
        createTime: '',
        creator: '',
        graduallyRate: 0,
        id: 0,
        interestDiscount: 0,
        interestRate: 0,
        inviteCount: 0,
        level: 0,
        paymentAmount: 0,
        siteId: 0,
        updateTime: '',
        updater: '',
    },
})

const { res: next } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF%E4%BC%9A%E5%91%98/getNextUserLevelUsingGET_1
    url: '/member/getNextUserLevel',
    initialValues: {
        isFullLevel: false,
        nextLevelInviteNumber: 0,
        nextLevelRechargeAmount: 0,
        remainInviteNumber: 0,
        remainRechargeAmount: 0,
        userInviteNumber: 0,
        userRechargeAmount: 0,
    },
})

const { t, locale } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '等级特权',
            _upgrade: '升级条件',
            _invite: '再邀请好友（有充值）',
            _deposit: '再充值',
            _discount: '利息折扣',
            _rebate: '逐级返佣比例',
            _person: '人',
        },
        [LANGUAGE.zhHK]: {
            _title: '等级特权',
            _upgrade: '升级条件',
            _invite: '再邀请好友（有充值）',
            _deposit: '再充值',
            _discount: '利息折扣',
            _rebate: '逐级返佣比例',
            _person: '人',
        },
        [LANGUAGE.enUS]: {
            _title: 'Tier Privileges',
            _upgrade: 'Upgrade Requirements',
            _invite: 'Invite Additional Friends (with Deposit)',
            _deposit: 'Deposit More',
            _discount: 'Interest Discount',
            _rebate: 'Tiered Commission Rebate Ratio',
            _person: 'Person',
        },
    },
})

const isEn = locale.value === LANGUAGE.enUS

defineOptions({ name: 'vip' })
</script>

<style scoped>

</style>
