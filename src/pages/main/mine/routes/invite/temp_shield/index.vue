<template>
    <div class="h-full invite">
        <c-header :title="t('_title')" transparent/>

        <div class="with-header-container">
            <div class="text-center text-[var(--title)]">
                <div
                    data-aos="fade-left"
                    data-aos-delay="50"
                    class="text-4xl"
                >
                    {{ t('_subhead') }}
                </div>

                <div
                    data-aos="fade-left"
                    data-aos-delay="100"
                    class="mt-1 whitespace-pre-line"
                >
                    {{ t('_description') }}
                </div>

                <div
                    data-aos="fade-left"
                    data-aos-delay="150"
                    class="mx-auto w-max mt-28 rounded-full h-9 px-2.5 text-xs flex-middle gap-2.5 bg-white/30 go"
                    @click="$router.push('/vip')"
                >
                    <div>{{ t('_vip') }}</div>

                    <img
                        src="./assets/go.png"
                        alt="go"
                        class="size-6"
                    >
                </div>
            </div>

            <c-card class="my-4">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="block" @click="show = true">
                        <van-icon :size="20" name="qr"/>

                        <div>{{ t('_qrcode') }}</div>
                    </div>

                    <div class="block" @click="onCopy(res.inviteCode)">
                        <div class="h-5 leading-5 flex-middle gap-2">
                            <span>{{ res.inviteCode }}</span>
                            <c-icon size="14" name="copy"/>
                        </div>

                        <div>{{ t('_referrer') }}</div>
                    </div>
                </div>

                <van-button
                    block
                    type="primary"
                    @click="onCopy()"
                >
                    <van-icon name="link-o"/>
                    {{ t('_link') }}
                </van-button>
            </c-card>

            <c-card>
                <Divider :size="[ 1, 64 ]" class="flex-middle h-24 text-text">
                    <div class="flex-1 text-center">
                        <div class="text-title">{{ t('vip.trade_rebate_ratio') }}</div>
                        <c-amount
                            class="text-[var(--number)] text-3xl"
                            :amount="res.commissionRate"
                            percent
                        />
                    </div>

                    <div class="flex-1 text-center">
                        <div class="text-title">{{ t('vip.interest_rebate_ratio') }}</div>
                        <c-amount
                            class="text-[var(--number)] text-3xl"
                            :amount="res.interestRate"
                            percent
                        />
                    </div>
                </Divider>

                <div class="pt-4 flex-between border-t border-border text-sm text-title">
                    <div>{{ t('_commission') }}</div>
                    <c-amount
                        :amount="res.totalCommission"
                        currency="CNY"
                    />
                </div>
            </c-card>
        </div>
    </div>

    <van-popup
        v-model:show="show"
    >
        <img
            :src="qrcode"
            alt="QR Code"
            class="size-[200px]"
        />
    </van-popup>
</template>

<script setup>
import { useInvite } from '../composables/index.js'

const {
    useQRCode,
    Divider,
    show,
    href,
    res,
    copy,
    onCopy,
    qrcode,
    t,
} = useInvite()

defineOptions({ name: 'invite' })
</script>

<style scoped>
.invite {
    background-position: 0 100px, center 129px, 0 0;
    background-repeat: no-repeat;
    background-size: 171px 154px, 242px 163px, 100% 100%;
}

[data-theme="light"] .invite {
    background-image: url('./assets/bg_1.png'), url('./assets/bg_2.png'), linear-gradient(180deg, #E64A41 0%, #FFF3EF 100%);
    --title: #FFF5E5;
    --text: #B7655F;
    --number: #EE7D75;
}

[data-theme="dark"] .invite {
    background-image: url('./assets/bg_1.png'), url('./assets/bg_2.png'), linear-gradient(180deg, #14183C 0%, #0E101E 100%);
    --title: #646ADC;
    --text: #8F94F5;
    --number: var(--text);
}

.block {
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
    border-radius: 8px;
    color: var(--text);
}

[data-theme="light"] .block {
    border: 1px solid #FFEDED;
    background: linear-gradient(180deg, #FFE1DE 0%, #FFF8F8 100%);
}

[data-theme="dark"] .block {
    border: 1px solid transparent;
    background: #0D1233;
    backdrop-filter: blur(10px);
}

[data-theme="light"] .go {
    color: #873F3A;
}

[data-theme="dark"] .go {
    color: #0D1233;
}
</style>
