<template>
    <c-header :title="$t(`header.password_${passwordType.type}`)"/>
    <div class="pt-4">
        <van-cell-group
            inset
            class="mb-2.5!"
            data-aos="fade-left"
            v-for="(key, i) in cell"
            :data-aos-delay="i * 50"
            :key="key"
        >
            <van-cell
                :title="$t(`modify_password.by_${key}`)"
                :to="`/modify/${passwordType.type}/${key}`"
                is-link
            />
        </van-cell-group>
    </div>
</template>

<script setup>
import { usePasswordType } from './hooks.js'

const passwordType = usePasswordType()

const cell = [ 'otp', 'original' ]

defineOptions({ name: 'modify' })
</script>

<style scoped>
:deep(.van-cell-group) {
    margin-bottom: 10px !important;
}
</style>
