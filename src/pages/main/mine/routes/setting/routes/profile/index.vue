<template>
    <c-header :title="t('profile.title')"/>

    <div class="with-header-container">
        <van-cell-group>
            <van-cell
                data-aos="fade-left"
                :title="t('profile.avatar')"
                :to="{ name: 'avatar' }"
                is-link
            >
                <template #value>
                    <c-avatar
                        class="size-6 ml-auto"
                        :avatar="$profile.avatar"
                    />
                </template>
            </van-cell>

            <van-cell
                data-aos="fade-left"
                v-for="({ title, value, to, key }, i) in cell"
                :data-aos-delay="`${(i + 1) * 50}`"
                :title
                :value
                :key
                :to
                :is-link="!!to"
            >
                <template v-if="key === 'mobile'" #right-icon>
                    <ModifyPhone :phone="value"/>
                </template>
            </van-cell>
        </van-cell-group>
    </div>
</template>

<script setup>
import { useProfile } from './composables/index.js'

const {
    $profile,
    t,
    cell,
    ModifyPhone,
} = useProfile()

defineOptions({ name: 'profile' })
</script>

<style scoped>
.van-cell-group {
    text-transform: none;
}

:deep(.van-cell__value) {
    display: inline-flex;
    justify-content: flex-end;
    align-items: center;
}
</style>
