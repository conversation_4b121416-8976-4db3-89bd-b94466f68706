import ModifyPhone from '../modify/mobile.vue'

export const useProfile = () => {

    const { $profile } = storeToRefs(useProfileStore())

    const cell = computed(() => {
        const { nickname, realName, email, mobile } = $profile.value

        return [
            {
                title: t('profile.nickname'),
                value: nickname,
                to: '/profile/update/nickname',
                key: 'nickname',
            },
            {
                title: t('profile.name'),
                value: realName,
                key: 'realName',
            },
            {
                title: t('profile.email'),
                value: email,
                key: 'email',
            },
            {
                title: t('profile.mobile'),
                value: mobile,
                key: 'mobile',
            },
        ]
    })

    const { t } = useI18n()

    return {
        $profile,
        t,
        cell,
        ModifyPhone,
    }
}
