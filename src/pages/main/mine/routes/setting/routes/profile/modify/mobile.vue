<template>
    <van-dialog
        teleport="body"
        v-model:show="show"
        title="修改手机号"
        show-cancel-button
        :before-close="handleBeforeClose"
    >
        <van-form ref="formRef">
            <van-cell-group inset>
                <van-field
                    v-model="formState.currentMobile"
                    name="currentMobile"
                    disabled
                />

                <van-field
                    v-model="formState.newMobile"
                    name="newMobile"
                    placeholder="请输入新手机号"
                    autocomplete="off"
                    maxlength="11 "
                    :rules="[
                        { required: true, message: '请填写手机号' },
                        { validator: validateMobile, message: '手机号格式不正确' },
                    ]"
                />

                <van-field
                    v-model="formState.smsCode"
                    name="smsCode"
                    autocomplete="off"
                    placeholder="请输入验证码"
                    maxlength="6"
                    :rules="[
                        { required: true, message: '请输入验证码' },
                        { validator: validateOtp, message: '验证码格式不正确' },
                    ]"
                >
                    <template #button>
                        <van-button
                            type="primary"
                            size="mini"
                            :disabled="otpDisabled"
                            :loading="otpLoading"
                            @click="onSendOtp(formState.newMobile)"
                        >
                            {{ otpText }}
                        </van-button>
                    </template>
                </van-field>
            </van-cell-group>
        </van-form>
    </van-dialog>
    <van-button @click="handleBtnClick" class="ml-l25" :loading="loading" size="mini" type="primary">修改</van-button>
</template>

<script setup>
const { t } = useI18n()
const show = ref(false)
const formRef = ref(null)

// 获取是否开启登录时网易验证开关
const { res: isNeedAuth, onRefresh: onRefreshAuth } = useRequest({
    url: '/yidun/getEnable', initialValues: false, params: {
        sceneType: 2,
    },
})

const {
    otpText,
    otpLoading,
    otpDisabled,
    onSendOtp,
} = useOtp({ type: OTP_TYPES.UPDATE_MOBILE }, computed(() => isNeedAuth.value))
const { dispatch_refreshProfile } = useProfileStore()

const { phone } = defineProps({
    phone: {
        required: true,
        type: [ Number, String ],
    },
})

const formState = reactive({
    currentMobile: phone,
    newMobile: '',
    smsCode: '',
})

const handleBtnClick = () => {
    show.value = true
}

function validateMobile(val) {
    return REGULAR.MOBILE.test(val)
}

function validateOtp(val) {
    return REGULAR.OTP.test(val)
}

const handleReset = () => {
    Object.assign(formState, {
        currentMobile: phone,
        newMobile: '',
        smsCode: '',
    })
}

const handleBeforeClose = async (action) => {
    if (action === 'confirm') {
        try {
            await formRef.value?.validate()
            await onSubmit()
            show.value = false
        } catch (error) {
            // 校验失败或提交出错，不关闭弹窗
            console.warn('校验失败或接口出错', error)
        }
    } else {
        handleReset()
        show.value = false
    }
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    await api_put({
        url: '/member/updateMobile',
        params: {
            currentMobile: phone,
            newMobile: formState.newMobile,
            smsCode: formState.smsCode,
        },
    })

    handleReset()
    showSuccessToast(t('operation.successfully'))
    await dispatch_refreshProfile()
    show.value = false
})

defineOptions({
    title: 'ModifyPhone',
})
</script>

<style scoped>

.ml-l25 {
    margin-left: 10px;
}

:deep(.van-cell-group--inset) {
    margin: 0 !important;
}
</style>
