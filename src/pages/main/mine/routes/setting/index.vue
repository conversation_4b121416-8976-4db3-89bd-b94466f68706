<template>
    <c-header :title="$t('header.setting')" :disabled="loading"/>

    <div class="with-header-container flex flex-col space-y-2.5">
        <van-cell-group
            class="not-last:mb-2.5"
            v-for="(s, i) in setting"
            :key="i"
        >
            <van-cell
                data-aos="fade-left"
                v-for="({ title, to, value, key, handler }, index) in s"
                :data-aos-delay="`${(i + index) * 50}`"
                :key="index"
                :title
                :value
                :to
                is-link
                @click="handler ? handler() : onAction(key)"
            />
        </van-cell-group>

        <div
            class="mt-auto"
            v-if="$isLogin"
            data-aos="fade-left"
            data-aos-delay="200"
        >
            <van-button
                block
                type="danger"
                :loading
                @click="onLogoutConfirm"
            >
                {{ $t('auth.logout') }}
            </van-button>
        </div>
    </div>

    <van-action-sheet
        :cancel-text="t('operation.cancel')"
        v-model:show="visible"
    >
        <div class="van-action-sheet__content">
            <div
                class="van-action-sheet__item"
                v-for="({ title, key, active }) in optionConfig.actions"
                :key
                @click="onSelect(key, active)"
            >
                <c-icon :name="key"/>

                <span class="ml-2.5 flex-1 text-sm" :class="[ active ? 'text-active' : 'text-text' ]">
                    {{ title }}
                </span>

                <van-icon
                    v-if="active"
                    name="checked"
                    class="text-active"
                />
            </div>
        </div>
    </van-action-sheet>
</template>

<script setup>
import _ from 'lodash'

const { config, actionType, optionConfig } = useSystemSetting()

const { $isLogin } = storeToRefs(useProfileStore())

const { loading, onLogoutConfirm } = useLogout()

const _utils_getStorageUsage = (storage) => {
    let total = 0
    _.entries(storage).forEach(e => {
        const [ key, val ] = e
        total += key.length + val.length
    })

    return total * 2 / 1024
}

const setting = computed(() => [
    [
        {
            title: t('profile.title'),
            to: '/profile',
        },
    ],
    [
        {
            title: t('header.password_account'),
            to: '/modify/account',
        },
        {
            title: t('header.password_financial'),
            to: '/modify/financial',
        },
    ],
    config.value,
    [
        {
            title: t('_cache'),
            value: `${(_utils_getStorageUsage(sessionStorage) + _utils_getStorageUsage(localStorage)).toFixed(2)}KB`,
            handler: async () => {
                await showConfirmDialog({
                    title: t('_cache_tip'),
                    message: t('_cache_confirm'),
                })
                sessionStorage.clear()
                localStorage.clear()
                logout()
            },
        },
    ],
])

const visible = ref(false)

const onAction = type => {
    if (type) {
        actionType.value = type
        visible.value = true
    }
}

const onSelect = (key, active) => {
    if (!active) unref(optionConfig).onSelect(key)

    visible.value = false
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _cache: '清理缓存',
            _cache_tip: '清除缓存后您需要重新登录',
            _cache_confirm: '是否确认要清除？',
        },
        [LANGUAGE.zhHK]: {
            _cache: '清理缓存',
            _cache_tip: '清除缓存后您需要重新登录',
            _cache_confirm: '是否确认要清除？',
        },
        [LANGUAGE.enUS]: {
            _cache: 'Clear Cache',
            _cache_tip: 'You will relogin after clearing cache',
            _cache_confirm: 'Are you sure you want to clear it?',
        },
    },
})

defineOptions({ name: 'setting' })
</script>

<style scoped>
</style>
