<template>
    <van-skeleton :loading="!initial">
        <template #template>
            <div class="w-full">
                <c-card
                    class="mb-2.5"
                    v-for="n in 5"
                    :key="n"
                >
                    <van-skeleton-paragraph/>
                    <van-skeleton-paragraph/>
                    <van-skeleton-paragraph/>
                    <van-skeleton-paragraph/>
                </c-card>
            </div>
        </template>

        <template v-if="data.length">
            <c-card
                class="mb-2.5"
                v-for="({ id, name, content, startTime, endTime, isCompleted, isReceived, amount, amountType, availableNum }) in data"
                :key="id"
                :title="name"
            >
                <template #extra>
                    <StatusRender :status="isCompleted"/>
                </template>
                <c-description :label="t('_content')">
                    {{ content }}
                </c-description>
                <c-description :label="t('_award')">
                    <c-amount :amount :suffix="amountType === 1 ? t('_amount') : t('account.interest')"/>
                </c-description>
                <c-description :label="t('_times')" :value="availableNum"/>

                <div class="leading-7 text-xs text-right">
                    {{ t('_range') }}：
                    {{ _time_formate(startTime, endTime) }}
                </div>

                <c-submit
                    class="mt-2.5"
                    size="small"
                    :disabled="!isCompleted || (isCompleted && isReceived)"
                    :loading
                    @click="onGet(id)"
                >
                    {{ isReceived ? t('_got') : t('_get') }}
                </c-submit>
            </c-card>
        </template>

        <van-empty v-else/>
    </van-skeleton>
</template>

<script setup>
import { Icon } from 'vant'

const { onRefresh, data } = defineProps({
    initial: {
        type: Boolean,
        required: true,
    },
    data: {
        type: Array,
        required: true,
    },
    onRefresh: {
        type: Function,
        required: true,
    },
})

const _time_formate = (t1, t2) =>
    !t1 && !t2 ? t('长期有效') : t1 === t2 ? t('今天') : `${t1}-${t2}`

const { locale } = useI18n()

const StatusRender = ({ status }) => {
    let color,
        text,
        icon

    if (status) {
        color = 'text-green'
        text = t('_finished')
        icon = 'success'
    } else {
        color = 'text-red'
        text = t('_unfinished')
        icon = 'cross'
    }

    return h(
        'div',
        {
            class: [ 'text-xs', color ],
        },
        [
            h(
                Icon,
                {
                    name: icon,
                },
            ),
            h(
                'span',
                text,
            ),
        ],
    )
}

const [ onGet, loading ] = useFetchLoading(async (taskId) => {
    const res = await api_get({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%B4%BB%E5%8A%A8/receiveRewardUsingGET_1
        url: '/activity/receiveReward',
        params: {
            taskId,
        },
    })
    showSuccessToast(t('_successfully', [ res.amount, res.type === 1 ? t('_amount') : t('account.interest') ]))
    await onRefresh()
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _finished: '已完成',
            _unfinished: '未完成',
            _content: '活动内容',
            _award: '任务奖励',
            _times: '可完成次数',
            _amount: '现金',
            _range: '有效期',
            _get: '领取',
            _got: '已领取',
            _successfully: '成功领取{0}{1}',
            长期有效: '长期有效',
            今天: '今天',
        },
        [LANGUAGE.zhHK]: {
            _finished: '已完成',
            _unfinished: '未完成',
            _content: '活动内容',
            _award: '任务奖励',
            _times: '可完成次数',
            _amount: '现金',
            _range: '有效期',
            _get: '领取',
            _got: '已领取',
            _successfully: '成功領取{0}{1}',
            长期有效: '长期有效',
            今天: '今天',
        },
        [LANGUAGE.enUS]: {
            _finished: 'Finished',
            _unfinished: 'Unfinished',
            _content: 'Activity Content',
            _award: 'Task Reward',
            _times: 'Completable Times',
            _amount: 'Cash',
            _range: 'Validity Period',
            _get: 'Get',
            _got: 'Got',
            _successfully: 'Successfully Claimed{0}{1}',
            长期有效: 'Long Term Valid',
            今天: 'Today',
        },
    },
})

defineOptions({ name: 'MissionList' })
</script>

<style scoped>

</style>
