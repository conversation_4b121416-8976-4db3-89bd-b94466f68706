<template>
    <c-header :title="t('header.mission')"/>

    <div class="container">
        <van-calendar
            class="mb-2.5"
            :title="t('_sign_activity')"
            switch-mode="month"
            first-day-of-week="1"
            :poppable="false"
            :show-subtitle="false"
            :show-confirm="false"
            readonly
        >
            <template #text="props">
                <DateRender v-bind="props"/>
            </template>

            <template #footer>
                <c-submit
                    class="mb-3"
                    size="small"
                    :loading="signLogLoading || signLoading"
                    :disabled="isSigned"
                    @click="onSign"
                >
                    {{ isSigned ? t('_signed') : t('_sign') }}
                </c-submit>
            </template>
        </van-calendar>

        <van-tabs
            data-aos="fade-left"
            type="card"
        >
            <van-tab
                class="pt-2.5"
                v-for="({ title, key, data }) in tabs"
                :key
                :title
                :name="key"
            >
                <MissionList
                    :initial
                    :data
                    :onRefresh
                />
            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup>
import dayjs from 'dayjs'

import Amount from '@/components/Amount/index.vue'
import Icon from '@/components/Icon/index.vue'
import MissionList from './components/MissionList.vue'

const tabs = computed(() => [
    {
        title: t('_daily'),
        key: 'daily',
        data: res.value.daily,
    },
    {
        title: t('_trade'),
        key: 'trade',
        data: res.value.trade,
    },
    {
        title: t('_newbie'),
        key: 'newbie',
        data: res.value.newUser,
    },
])

const { res, initial, onRefresh } = useRequest({
    url: '/activity/tasks',
    needLogin: true,
    initialValues: {
        daily: [],
        newUser: [],
        trade: [],
    },
})

const { res: signLog, loading: signLogLoading, onRefresh: onRefreshSignLog } = useRequest({
    url: '/activity/signIn/log',
    initialValues: [],
})

const isSigned = computed(() => signLog.value.find(e => e.date === dayjs().format(TIME_FORMAT.YMD))?.hasSign ?? false)

const DateRender = ({ date, type, text }) => {
    const today = type === 'selected',
        dateDetails = signLog.value.find(e => e.date === dayjs(date).format(TIME_FORMAT.YMD))

    return h(
        'div',
        {
            class: 'text-xs font-digit',
            style: {
                height: 'var(--van-calendar-selected-day-size)',
            },
        },
        [
            h(
                'div',
                {
                    class: today ? 'text-white' : 'text-title',
                },
                text,
            ),
            h(
                Icon,
                {
                    prefix: 'mine',
                    size: 22,
                    name: dateDetails?.hasSign ? 'gift_open' : 'gift',
                    style: {
                        filter: dateDetails?.hasSign ? undefined : 'grayscale(90%)',
                    },
                },
            ),
            dateDetails?.amount > 0
                ? h(
                    Amount,
                    {
                        amount: dateDetails.amount,
                        prefix: '¥',
                        precision: 0,
                    },
                )
                : null,
        ],
    )
}

const [ onSign, signLoading ] = useFetchLoading(async () => {
    await api_post({
        url: '/activity/signIn',
    })

    onRefreshSignLog()

    showSuccessToast(t('_sign_successfully'))
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _daily: '每日任务',
            _trade: '交易任务',
            _newbie: '新手任务',
            _sign_activity: '签到活动',
            _sign: '签到',
            _signed: '已签到',
            _sign_successfully: '签到成功',
        },
        [LANGUAGE.zhHK]: {
            _daily: '每日任务',
            _trade: '交易任务',
            _newbie: '新手任务',
            _sign_activity: '签到活动',
            _sign: '签到',
            _signed: '已签到',
            _sign_successfully: '签到成功',
        },
        [LANGUAGE.enUS]: {
            _daily: 'Daily',
            _trade: 'Transaction',
            _newbie: 'Newbie',
            _sign_activity: 'Sign Activity',
            _sign: 'Sign',
            _signed: 'Signed',
            _sign_successfully: 'Sign Successfully',
        },
    },
})

defineOptions({ name: 'mission' })
</script>

<style scoped>
:deep(.van-tabs__nav--card) {
    margin: 0;
}

.container {
    height: calc(100% - var(--header-height));
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px 10px 10px;
}

:deep(.van-calendar__body) {
    flex: auto;
    height: 400px !important;
}
</style>
