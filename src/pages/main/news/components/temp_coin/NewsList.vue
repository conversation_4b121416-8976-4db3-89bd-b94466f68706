<template>
    <c-record
        :finished="$newsFinished"
        v-model:refresh-loading="$newsRefreshLoading"
        v-model:load-loading="$newsLoadLoading"
        :onRefresh="dispatch_refreshNews"
        :onLoadMore="dispatch_loadMoreNews"
    >
        <van-skeleton :loading="!$newsInitial">
            <template #template>
                <div class="w-full overflow-hidden">
                    <div
                        class="flex-middle gap-2.5 not-last:mb-3"
                        v-for="key in 10"
                        :key
                        data-aos="fade-left"
                        :data-aos-delay="`${key * 50}`"
                        data-aos-anchor="#app"
                    >
                        <van-skeleton-image/>

                        <div class="flex-1">
                            <van-skeleton-title/>
                            <van-skeleton-paragraph/>
                            <van-skeleton-paragraph/>
                        </div>
                    </div>
                </div>
            </template>
        </van-skeleton>

        <div
            data-aos="fade-left"
            data-aos-anchor="#app"
            class="flex-middle gap-2.5 py-2.5 px-2.5 bg-bg4"
            v-for="(item, i) in $news"
            :data-aos-delay="50 * i"
            :key="item.id"
            @click="$newsDetails = item; $router.push('/news')"
        >

            <img
                :src="item.url"
                alt="news-cover"
                class="w-[100px] h-16 rounded"
            >

            <div class="flex-1">
                <div class="van-multi-ellipsis--l2  text-sm">{{ item.title }}</div>

                <div class="flex-between text-regular text-xs mt-2">
                    <span>{{ item.comefrom }}</span>
                    <span>{{ item.publishTime }}</span>
                </div>
            </div>

        </div>
    </c-record>
</template>

<script setup>
import { $newsDetails } from '../../store.js'

const newsStore = useNewsStore(),
    { dispatch_refreshNews, dispatch_loadMoreNews } = newsStore,
    { $news, $newsInitial, $newsFinished, $newsRefreshLoading, $newsLoadLoading } = storeToRefs(newsStore)

defineOptions({ name: 'NewsList' })
</script>

<style scoped>
</style>
