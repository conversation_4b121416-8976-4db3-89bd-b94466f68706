<template>
    <c-header title="AI"/>

    <div class="with-header-container">
        <c-card class="bg-primary">
            <div class="text-center text-white whitespace-pre-wrap">
                {{ t('_slogan') }}
            </div>
        </c-card>

        <c-card class="mt-2.5">
            <Search
                class="bg-controller_bg"
                :placeholder="t('_placeholder')"
                searchBtn
                :loading
                :disabled="!content"
                v-model="content"
                @search="onSearch"
            />

            <div v-if="!reply.length" class="mt-5 flex justify-center">
                <img class="w-54 h-54" :src="uri" alt="">
            </div>

            <div class="mt-4">
                <span
                    ref="chatRef"
                    class="inline text-paragraph text-sm whitespace-pre-line"
                />
            </div>
        </c-card>
    </div>
</template>

<script setup>
import Typed from 'typed.js'

import Search from '@/components/Search/index.vue'

const uri = computed(() => {
    return `/skins/templates/${G_TEMPLATE}/${G_THEME}/ai.png`
})
const content = ref(''),
    reply = ref([])

const chatRef = useTemplateRef('chatRef')
const [ onSearch, loading ] = useFetchLoading(async () => {
    if (content.value) {
        const res = await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/Ai%E6%8A%95%E8%B5%84%E9%A1%BE%E9%97%AE/chatUsingPOST_1
            url: '/ai/chat',
            params: {
                question: content.value,
            },
        })

        reply.value.push(content.value)
        reply.value.push(res.reply)

        const typed = new Typed(chatRef.value, {
            strings: [ res.reply ],
            typeSpeed: 30,
        })

        await nextTick()

        typed.start()
    }
}, { keepLatest: true })

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _slogan: '智能AI帮您轻松理财\n开启智能投资新时代',
            _placeholder: '请输入要分析的内容',
            _tip: 'AI智能分析',
        },
        [LANGUAGE.zhHK]: {
            _slogan: '智能AI帮您轻松理财\n开启智能投资新时代',
            _placeholder: '请输入要分析的内容',
            _tip: 'AI智能分析',
        },
        [LANGUAGE.enUS]: {
            _slogan: 'Smart AI helps you manage your finances easily\nOpening a new era of smart investment',
            _placeholder: 'Please enter the content to be analyzed',
            _tip: 'AI Intelligent Analysis',
        },
    },
})

defineOptions({ name: 'ai' })
</script>

<style scoped>
:deep(.c-search__input) {
    background: var(--controller_bg);
}
</style>
