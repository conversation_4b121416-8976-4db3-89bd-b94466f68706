<template>
    <div class="with-header-container__noPadding">
        <TopTabs/>

        <component
            :is="activeCom"
        />
    </div>
</template>

<script setup>
import { computed } from 'vue'
import TopTabs from '@/pages/main/account/components/TopTabs.vue'
import Contract from '@/pages/main/account/components/Contract.vue'
import Spot from '@/pages/main/account/components/Spot.vue'
import { accountActiveTab } from '@/store'
import { $theme } from '@/store/index.js'
import bgImageLight from '/skins/templates/_TEMPLATE_/_THEME_/bg_light2.png'
import bgImageDark from '/skins/templates/_TEMPLATE_/_THEME_/bg_dark2.png'


const activeCom = computed(() => {
    return accountActiveTab.value === 'spot' ? Spot : Contract
})

onMounted(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        appEl.style.background = `var(--page) url(${$theme.value === 'light' ? bgImageLight : bgImageDark}) no-repeat top center`
        appEl.style.backgroundSize = 'contain, auto'
    }
})

onBeforeUnmount(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        if (appEl) {
            appEl.style.background = ''
            appEl.style.backgroundSize = ''
            appEl.style.backgroundPositionY = ''
        }
    }
})

defineOptions({ name: 'account' })
</script>
