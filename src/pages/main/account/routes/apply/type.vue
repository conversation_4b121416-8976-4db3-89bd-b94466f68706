<template>
    <c-header :title>
        <template #right>
            <van-icon
                name="todo-list-o"
                size="20"
                @click="$router.push('/contract/apply/record')"
            />
        </template>
    </c-header>

    <div class="with-header-container">
        <CellGroup class="space-y-2.5">
            <Cell
                data-aos="fade-left"
                class="bg-primary rounded-xl"
                v-for="({ label, value, key }, i) in options"
                :data-aos-delay="i * 50"
                :key
                :icon="iconConf"
                arrow
                :to="`/contract/apply/${contractType}/${value}`"
            >
                <template #title>
                    <span class="text-white">{{ label }}</span>
                </template>
            </Cell>
        </CellGroup>
    </div>
</template>

<script setup>
import { CellGroup, Cell } from '@/components/Cell'

const { contractType, contractTypeOptions } = useContractType()

const options = ref([])

useRequest({
    url: '/contract/config/getOpenContractType',
    initialValues: [],
    onSuccess: res => {
        options.value = contractTypeOptions.filter(e => res.includes(e.visibleIndex))
    },
})

const { t } = useI18n()

// GP 文字前有图标
const iconConf = computed(() => {
    if (G_TEMPLATE === 'GP') {
        return {
            prefix: 'account',
            name: `contract_${key}`,
            size: 36,
        }
    } else {
        return null
    }
})

const backgroundStyle = computed(() => {
    if (G_THEME === 'GP') {
        return {
            background: '#3A5BCD',
        }
    } else if (G_THEME === 'golden') {
        return {
            background: 'var(--btn-bg)',
        }
    }
})

const title = t('contract.apply', { name: +contractType === 1 ? t('contract.title') : t('futures.title') })

defineOptions({ name: 'account-apply-type' })
</script>
