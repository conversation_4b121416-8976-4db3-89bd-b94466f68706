<template>
    <c-header :title/>

    <div class="with-header-container">
        <c-card data-aos="fade-left">
            <!-- 合约类型: 市场类型 -->
            <c-picker
                :title="t('contract.market')"
                :loading="!initial"
                :options="marketOptions"
                v-model:index="selectedIndex.market"
            >
                <template #extra>
                    <van-icon
                        v-if="(type.bonus || type.experience) && rule"
                        name="question-o"
                        @click="popup = true"
                    />
                </template>
            </c-picker>
            <!-- 合约类型: 市场类型 -->

            <!-- 周期 -->
            <c-picker
                class="my-6"
                :title="t('contract.period')"
                :loading="!initial"
                :options="periodOptions"
                v-model:index="selectedIndex.period"
            />
            <!-- 周期 -->

            <!-- 倍数 -->
            <c-picker
                class="mb-2"
                :title="t('contract.multiple')"
                :loading="!initial"
                :options="multipleOptions"
                v-model:index="selectedIndex.multiple"
            />
            <!-- 倍数 -->

            <!-- 申请基础信息 -->
            <!-- 彩金、体验赠送天数 -->
            <c-description
                v-if="type.bonus || type.experience"
                class="font-digit"
                :label="t('contract.gift_days')"
            >
                {{ t('contract.days', [ currentRes.giveDay ]) }}
            </c-description>
            <!-- 彩金、体验赠送天数 -->

            <template v-if="!type.experience">
                <template v-if="type.bonus">
                    <!-- 彩金合约赠送金额 -->
                    <c-description :label="giftAmountLabel">
                        <c-amount
                            :amount="giftAmount"
                            :currency="currentMarketCurrency"
                        />
                        <!-- 彩金合约赠送金额 -->
                    </c-description>
                </template>

                <!-- 可用余额 -->
                <c-description :label="t('account.balance')">
                    <c-amount :amount="$spot.usableCash" :currency="CURRENCY.CNY"/>
                </c-description>
                <!-- 可用余额 -->

                <!-- 利息券 -->
                <c-description v-if="!type.expand && !type.replenish" :label="t('account.interest')">
                    <c-amount :amount="$spot.interestCash" :currency="CURRENCY.CNY"/>
                </c-description>
                <!-- 利息券 -->
            </template>
            <!-- 申请基础信息 -->
        </c-card>

        <c-card
            class="mt-3"
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <!-- 保证金 -->
            <c-select
                v-if="!type.experience"
                inputable
                showLabel
                :label="type.replenish ? t('contract.replenish_amount') : t('contract.principal')"
                :columns="presets"
                :disabled="type.renewal"
                v-model="customAmount"
                v-model:index="selectedIndex.amount"
            />
            <c-controller v-else :label="giftAmountLabel">
                <c-amount :amount="giftAmount" :currency="currentMarketCurrency"/>
            </c-controller>
            <!-- 保证金 -->

            <c-description-group
                class="mt-3"
                :items="details.show"
            />
        </c-card>

        <div
            data-aos="fade-left"
            data-aos-delay="150"
            data-aos-anchor="#app"
            class="text-xs my-4 flex-middle"
        >
            <van-checkbox
                class="mr-2"
                :icon-size="14"
                v-model="protocol"
            />

            <i18n-t
                tag="div"
                keypath="_protocol"
                scope="global"
            >
                <span class="text-link" @click="onShowProtocol">
                    {{ t('_protocol_name') }}
                </span>
            </i18n-t>
        </div>

        <div
            data-aos="fade-left"
            data-aos-delay="100"
            data-aos-anchor="#app"
        >
            <van-button
                type="primary"
                block
                :disabled="!customAmount || !protocol"
                :loading
                @click="detailsPopup = true"
            >
                {{ $t('operation.confirm') }}
            </van-button>
        </div>

        <van-popup
            class="p-4 overflow-hidden"
            style="width: min(90%, 640px * .9)"
            round
            v-model:show="detailsPopup"
        >
            <div class="text-title text-center">
                {{ title }}
            </div>

            <!-- 合约基础信息 -->
            <c-description-group
                class="mt-3"
                :items="baseItems"
            />
            <!-- 合约基础信息 -->

            <!-- 彩金合约 赠送金额 -->
            <c-description
                v-if="type.bonus"
                :label="giftAmountLabel"
            >
                <c-amount :amount="giftAmount" :currency="currentMarketCurrency"/>
            </c-description>
            <!-- 彩金合约 赠送金额 -->

            <!-- 合约申请详情 -->
            <c-description-group
                class="mb-3"
                :items="[
                    principal,
                    ...details.confirm
                ]"
            />
            <!-- 合约申请详情 -->

            <div class="flex gap-2.5">
                <van-button
                    block
                    @click="detailsPopup = false"
                >
                    {{ t('operation.cancel') }}
                </van-button>

                <van-button
                    type="primary"
                    block
                    @click="onSubmit"
                >
                    {{ t('form.submit') }}
                </van-button>
            </div>
        </van-popup>
    </div>

    <!-- 操盘协议 -->
    <ProtocolPopup/>

    <!-- 玩法说明 -->
    <van-popup
        position="bottom"
        v-model:show="popup"
    >
        <div class="h-10 leading-10 text-title font-semibold text-center">
            {{ t('_rule') }}
        </div>

        <div class="p-4 overflow-auto" style="max-height: 50vh;">
            {{ rule }}
        </div>
    </van-popup>
</template>

<script setup>
import currencyJs from 'currency.js'
import _ from 'lodash'

import { CURRENCY } from '@/config'
import Amount from '@/components/Amount/index.vue'
import router from '@/router.js'

const { contractType, contractTypeDict } = useContractType()

const { onShowProtocol, ProtocolPopup } = useProtocolPopup(4)

const { $spot } = storeToRefs(useAccountStore()),
    { dispatch_refreshAccount } = useAccountStore(),
    { dispatch_getCurrencyRateConfig } = useRateStore()

const { operation } = defineProps({
    operation: String,
})

const { params } = useRoute(),
    { back } = useRouter(),
    type = {
        // 股票配资
        stock: +contractType === 1,
        // 期货配资
        futures: +contractType === 2,
        // 普通合约
        normal: +params.type === 1,
        // 体验合约
        experience: +params.type === 2,
        // 彩金合约
        bonus: +params.type === 3,
        // 扩大保证金
        expand: operation === 'expand',
        // 追加合约
        replenish: operation === 'replenish',
        // 合约续费
        renewal: operation === 'renewal',
    }

// 选项索引
const selectedIndex = reactive({
    market: 0,
    period: 0,
    multiple: 0,
    amount: 0,
})

// 协议勾选
const protocol = ref(true)

// 确认弹窗
const detailsPopup = ref(false)

// 自定义输入金额
const customAmount = ref(0)

/**
 * @function _utils_contract_calc
 * @description 计算合约相关资金
 * @param principal {number} 保证金
 * @param multiple {number} 杠杆倍数
 * @param warnRatio {number} 亏损预警线比例
 * @param closeRatio {number} 亏损平仓线比例
 * @param bonusRatio {number} 彩金赠送比例
 * @returns {{ total: number, warn: number, close: number }}
 * total - 总操盘资金
 * warn - 亏损警戒线
 * close - 亏损平仓线
 * */
const _utils_contract_calc = ({ principal, multiple, warnRatio, closeRatio, bonusRatio }) => {
    let _principal = currencyJs(principal)

    // 有彩金赠送比例视为彩金合约
    if (bonusRatio) {
        // 彩金本金：本金 + 赠送彩金金额
        _principal = _principal.add(_principal.multiply(bonusRatio).divide(100))
    }

    // 杠杆金额 = (本金 * 倍数)
    const multipleAmount = _principal.multiply(multiple)

    // 总操盘资金
    const total = _principal.add(multipleAmount)

    // 亏损警戒线 = (本金 * 警戒线比例) + 杠杆金额
    const warn = _principal.multiply(warnRatio).divide(100).add(multipleAmount)

    // 亏损平仓线 = (本金 * (100 - 平仓线比例)) + 杠杆金额
    const close = _principal.multiply(100 - closeRatio).divide(100).add(multipleAmount)

    return {
        multipleAmount,
        total,
        warn,
        close,
    }
}

const onSuccess = onRefresh => {
    detailsPopup.value = false

    protocol.value = false

    Promise.all([
        onRefresh(),
        dispatch_refreshAccount(),
    ])

    showSuccessToast(t('operation.successfully'))
}

const giftAmountLabel = computed(() => t('contract.gift_amount'))

const useLogic = () => {
    // 扩大保证金、追加合约、续费
    if (type.expand || type.replenish || type.renewal) {
        let title, paramsType
        if (type.expand) {
            title = t('contract.expand')
            paramsType = 1
        } else if (type.replenish) {
            title = t('contract.replenish')
            paramsType = 2
        } else if (type.renewal) {
            title = t('contract.renewal')
            paramsType = 3
        }

        let marketOptions = ref([]),
            periodOptions = ref([]),
            multipleOptions = ref([]),
            presets = ref([]),
            baseItems = ref([])

        const { initial, res, onRefresh } = useRequest({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getDetailUsingGET_1
            url: `/contract/account/getDetail/${params.id}`,
            initialValues: {
                amountList: [],
                bonusAmountList: [],
                closeAmount: 0,
                closeValue: 0,
                giveDay: 0,
                id: 0,
                initCash: 0,
                interestRate: 0,
                marketType: STOCK_CONFIG.CN.symbol,
                multiple: 0,
                periodType: 0,
                totalCash: 0,
                totalFinance: 0,
                totalPower: 0,
                type: 0,
                warnAmount: 0,
                warnValue: 0,
            },
            onSuccess: res => {
                const {
                    type: contractType,
                    marketType,
                    periodType,
                    multiple,
                    amountList,
                    bonusAmountList,
                    initCash,
                } = res

                const label = {
                    market: t(`stock.${STOCK_CONFIG[marketType].symbol}`),
                    period: t(`contract.period_${periodType}`),
                    multiple: t('contract.multiples', [ multiple ]),
                }

                marketOptions.value = [ { label: label.market, value: marketType } ]
                periodOptions.value = [ { label: label.period, value: periodType } ]
                multipleOptions.value = [ { label: label.multiple, value: multiple } ]

                // 预设金额
                // 续期
                if (type.renewal) {
                    presets.value = [ { text: initCash, value: initCash } ]
                } else if (contractType === 3) {
                    // 彩金合约
                    presets.value = bonusAmountList.map(e => ({ text: e, value: e }))
                } else {
                    presets.value = utils_format_options(amountList, { text: 'applyAmount', value: 'applyAmount' })
                }

                customAmount.value = presets.value[0].text

                // 合约基础信息
                baseItems.value = [
                    { label: t('contract.market'), value: label.market },
                    { label: t('contract.period'), value: label.period },
                    { label: t('contract.multiple'), value: label.multiple },
                ]
            },
        })

        // 当前合约币种
        const currentMarketCurrency = computed(() => STOCK_CONFIG[res.value.marketType].currency)

        // 合约保证金
        const principal = computed(() => ({
            label: type.replenish ? t('contract.replenish') : t('contract.principal'),
            value: () => h(Amount, { amount: customAmount.value, currency: currentMarketCurrency.value }),
        }))

        // 合约详情
        const details = computed(() => {
            const currency = currentMarketCurrency.value,
                { rate } = dispatch_getCurrencyRateConfig(currency)

            const {
                initCash,
                type: contractType,
                multiple,
                periodType,
                closeValue,
                warnValue,
                interestRate,
                totalFinance,
                closeAmount,
                totalPower,
                warnAmount,
            } = res.value

            // 保证金
            const principal = type.replenish ? initCash : customAmount.value

            const { multipleAmount, total, warn, close } = _utils_contract_calc({
                principal,
                multiple,
                warnRatio: warnValue,
                closeRatio: closeValue,
                bonusRatio: contractType === 2 ? interestRate : undefined,
            })

            // 资金利率
            const percent = currencyJs(type.replenish ? totalFinance : multipleAmount).multiply(interestRate).divide(100).value

            const show = [
                {
                    label: t('contract.total_assets'),
                    value: () => h(Amount, { amount: type.replenish ? totalPower : total, currency }),
                },
                {
                    label: t('contract.warning'),
                    value: () => h(Amount, { amount: type.replenish ? warnAmount : warn, currency }),
                },
                {
                    label: t('contract.close'),
                    value: () => h(Amount, { amount: type.replenish ? closeAmount : close, currency }),
                },

                {
                    label: t('_pay'),
                    value: () => h(
                        Amount,
                        {
                            prefix: currentMarketCurrency.value !== CURRENCY.CNY ? '≈' : '',
                            amount: (type.replenish ? customAmount.value : (principal + percent)) / rate,
                            currency: CURRENCY.CNY,
                        },
                    ),
                },
            ]

            // 追加合约 不要资金利率
            if (operation !== 'replenish') {
                show.splice(2, 0, {
                    label: t('contract.rate'),
                    value: () => h(
                        'div',
                        {
                            class: 'flex-middle justify-end',
                        },
                        [
                            h(Amount, { amount: percent, currency }),
                            t(`contract.period_${periodType}_rate`),
                        ],
                    ),
                })
            }

            return {
                show,
                confirm: type.replenish ? show.slice(-1) : show,
            }
        })

        const [ onSubmit, loading ] = useFetchLoading(async () => {
            let url, _params

            // 合约续费
            if (type.renewal) {
                // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/renewalContractUsingPOST_1
                url = '/contract/account/renewalContract'
                _params = {
                    id: params.id,
                }
            } else {
                // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/addExpandMarginUsingPOST_1
                url = '/contract/account/addExpandMargin'
                _params = {
                    applyAmount: customAmount.value,
                    contractId: params.id,
                    type: paramsType,
                }
            }

            await api_post({
                url,
                params: _params,
            })

            onSuccess(onRefresh)

            back()
        })

        const currentMarket = computed(() => {
            const config = marketOptions.value[selectedIndex.market]
            if (config) return config
            return type.normal
                ? {
                    closeLossRadio: 0,
                    id: 0,
                    market: '',
                    warnLossRadio: 0,
                }
                : {
                    activityId: 0,
                    applyAmountList: [],
                    closeLossRadio: 0,
                    giveDay: 0,
                    giveRatio: 0,
                    interestRate: 0,
                    marketType: '',
                    multiple: 0,
                    riskId: 0,
                    shareRatio: 0,
                    warnLossRadio: 0,
                    giveAmount: 0,
                    activityRiskMap: [],
                    currency: '',
                    interestCash: 0,
                    useAmount: 0,
                }
        })

        return {
            title,

            initial,

            marketOptions,
            currentMarket,
            periodOptions,
            multipleOptions,
            presets,

            baseItems,
            details,
            principal,

            currentRes: res,
            giftAmount: 0,
            currentMarketCurrency,

            loading,
            onSubmit,
        }
    } else {
        // 申请合约逻辑
        const title = computed(() => t('contract.apply', { name: `【${contractTypeDict.get(+params.type)}】` }))

        // 当前响应体数据
        const currentRes = computed(() => {
            if (type.normal) {
                return res.value
            } else {
                return currentMarket.value
            }
        })

        // 合约市场类型选项配置
        const marketOptions = computed(() => {
            if (type.normal) {
                return utils_format_options(res.value.ruleMap, {
                    label: e => type.futures ? t('futures.title') : t(`stock.${STOCK_CONFIG[e.market].symbol}`),
                    value: 'id',
                })
            } else {
                return utils_format_options(res.value.activityRiskMap, {
                    label: e => type.futures ? t('futures.title') : t(`stock.${STOCK_CONFIG[e.marketType].symbol}`),
                    value: 'marketType',
                })
            }
        })
        const currentMarket = computed(() => {
            const config = marketOptions.value[selectedIndex.market]
            if (config) return config
            return type.normal
                ? {
                    closeLossRadio: 0,
                    id: 0,
                    market: '',
                    warnLossRadio: 0,
                }
                : {
                    activityId: 0,
                    applyAmountList: [],
                    closeLossRadio: 0,
                    giveDay: 0,
                    giveRatio: 0,
                    interestRate: 0,
                    marketType: '',
                    multiple: 0,
                    riskId: 0,
                    shareRatio: 0,
                    warnLossRadio: 0,
                    giveAmount: 0,
                    activityRiskMap: [],
                    currency: '',
                    interestCash: 0,
                    useAmount: 0,
                }
        })

        // 赠送金额
        const giftAmount = computed(() => {
            const { giveAmount, giveRatio } = currentMarket.value

            return giveAmount || currencyJs(giveRatio).multiply(customAmount.value).divide(100)
        })

        // 当前市场货币
        const currentMarketCurrency = computed(() => {
            const market = currentMarket.value?.market || currentMarket.value?.marketType || STOCK_CONFIG.CN.symbol

            return STOCK_CONFIG[market].currency
        })

        // 周期选项配置
        const periodOptions = computed(() => {
            return type.normal
                ? utils_format_options(res.value.contractConfigMap, {
                    label: ({ periodType }) => t(`contract.period_${periodType}`),
                    value: 'periodType',
                })
                : [ { label: t('contract.period_1'), value: 1 } ]
        })
        const currentPeriod = computed(() => periodOptions.value[selectedIndex.period])

        // 倍数配置
        const multipleOptions = computed(() => {
            if (type.normal) {
                return utils_format_options(res.value.contractConfigMap[selectedIndex.period]?.configList, {
                    label: ({ multiple }) => t('contract.multiples', [ multiple ]),
                    value: 'id',
                })
            } else {
                return [
                    {
                        label: t('contract.multiples', [ currentMarket.value?.multiple ?? 1 ]),
                        value: currentMarket.value?.multiple ?? 1,
                    },
                ]
            }
        })
        // 当前倍数
        const currentMultiple = computed(() => multipleOptions.value[selectedIndex.multiple])

        // 预设金额
        const presets = computed(() => {
            if (type.normal) {
                return utils_format_options(res.value.amountList, { text: 'applyAmount', value: 'applyAmount' })
            } else {
                return currentMarket.value.applyAmountList?.map(e => ({ text: e, value: e })) ?? []
            }
        })

        // 合约基础信息
        const baseItems = computed(() => {
            return [
                { label: t('contract.market'), value: currentMarket.value.label },
                { label: t('contract.period'), value: currentPeriod.value.label },
                { label: t('contract.multiple'), value: currentMultiple.value.label },
            ]
        })

        // 合约保证金
        const principal = computed(() => ({
            label: type.experience ? giftAmountLabel.value : t('contract.principal'),
            value: () => h(Amount, { amount: customAmount.value, currency: currentMarketCurrency.value }),
        }))

        const payDetails = ref({
            canUserCashCNY: 0,
            deductCanUseCashCNY: 0,
            deductInterestCashCNY: 0,
            discountInterestCNY: 0,
            exchangeRate: 0,
            interestCashCNY: 0,
            rate: 0,
            rateAmount: 0,
        })

        watchEffect(async () => {
            if (!_.isNaN(+customAmount.value) && +customAmount.value) {
                let url, params

                if (type.bonus) {
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getBonusContractAmountUsingGET_1
                    url = '/contract/account/getBonusContractAmount'
                    params = {
                        activityId: currentMarket.value.activityId,
                        activityRiskId: currentMarket.value.riskId,
                        applyAmount: +customAmount.value,
                        type: contractType,
                    }
                } else if (!type.experience) {
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getOrdinaryApplyAmountUsingGET_1
                    url = '/contract/account/getOrdinaryApplyAmount'
                    params = {
                        contractConfigId: currentMultiple.value.id,
                        riskId: currentMarket.value.id,
                        applyAmount: +customAmount.value,
                        type: contractType,
                    }
                }

                if (url) {
                    payDetails.value = await api_get({
                        url,
                        params,
                    })
                }
            }
        })

        // 合约详情
        const details = computed(() => {
            const currency = currentMarketCurrency.value

            // 实际杠杆倍数
            let multiple,
                // 亏损预警比例
                warnLossRadio,
                // 平仓比例
                closeLossRadio

            if (type.normal) {
                ({ warnLossRadio, closeLossRadio } = currentMarket.value ?? { warnLossRadio: 0, closeLossRadio: 0 })

                multiple = currentMultiple.value?.multiple ?? 0
            } else {
                ({ warnLossRadio, closeLossRadio } = currentRes.value)

                multiple = currentMultiple.value?.value ?? 0
            }

            // 保证金
            const principal = customAmount.value

            const { total, warn, close } = _utils_contract_calc({
                principal,
                multiple,
                warnRatio: warnLossRadio,
                closeRatio: closeLossRadio,
                bonusRatio: currentRes.value.giveRatio,
            })

            const { rateAmount, deductInterestCashCNY, deductCanUseCashCNY } = payDetails.value

            let base = [
                { label: t('contract.total_assets'), value: () => h(Amount, { amount: total, currency }) },
                { label: t('contract.warning'), value: () => h(Amount, { amount: warn, currency }) },
                { label: t('contract.close'), value: () => h(Amount, { amount: close, currency }) },
                {
                    label: t('contract.rate'),
                    value: () => h(
                        'div',
                        {
                            class: 'flex-middle justify-end',
                        },
                        [
                            h(Amount, { amount: rateAmount, currency }),
                            currentPeriod.value && t(`contract.period_${currentPeriod.value?.value}_rate`),
                        ],
                    ),
                },
                {
                    label: t('contract.interest'),
                    value: () => h(Amount, { amount: deductInterestCashCNY, currency: CURRENCY.CNY }),
                },
                {
                    label: t('_pay'),
                    value: () => h(
                        Amount,
                        {
                            prefix: currentMarketCurrency.value !== CURRENCY.CNY ? '≈' : '',
                            amount: deductCanUseCashCNY,
                            currency: CURRENCY.CNY,
                        },
                    ),
                },
            ]

            // 体验合约
            if (type.experience) base = base.slice(0, 3)

            return {
                show: base,
                confirm: base,
            }
        })

        const useFetchConfig = () => {
            if (type.normal) {
                return useRequest({
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%8E%B7%E5%8F%96%E5%90%88%E7%BA%A6%E9%85%8D%E7%BD%AE/getOrdinaryConfigInfoUsingGET_1
                    url: '/contract/config/getOrdinaryConfigInfo',
                    params: {
                        type: contractType,
                    },
                    initialValues: {
                        amountList: [],
                        contractConfigMap: [],
                        ruleMap: [],
                        currency: '',
                        interestCash: 0,
                        useAmount: 0,
                    },
                    onSuccess: res => {
                        customAmount.value = res.amountList[0].applyAmount
                    },
                })
            } else {
                return useRequest({
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%8E%B7%E5%8F%96%E5%90%88%E7%BA%A6%E9%85%8D%E7%BD%AE/getContractActivityConfigUsingGET_1
                    url: '/contract/config/getContractActivityConfig',
                    params: {
                        type: type.bonus ? 1 : 2,
                        parentType: contractType,
                    },
                    initialValues: {
                        activityRiskMap: [],
                        currency: '',
                        interestCash: 0,
                        useAmount: 0,
                    },
                    onSuccess: res => {
                        if (type.bonus) {
                            customAmount.value = res.activityRiskMap[0].applyAmountList[0]
                        } else {
                            customAmount.value = res.activityRiskMap[0].giveAmount
                        }
                    },
                })
            }
        }

        const { res, initial, onRefresh } = useFetchConfig()

        const [ onSubmit, loading ] = useFetchLoading(async () => {
            detailsPopup.value = false

            let api

            const submitParams = type.normal
                ? {
                    applyAmount: customAmount.value,
                    contractConfigId: currentMultiple.value.id,
                    riskId: currentMarket.value.id,
                }
                : {
                    activityId: currentRes.value.activityId,
                    activityRiskId: currentRes.value.riskId,
                    applyAmount: customAmount.value,
                }

            switch (+params.type) {
                // 普通合约
                case 1:
                // 期货
                case 4:
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/applyOrdinaryContractUsingPOST_1
                    api = '/contract/account/applyOrdinaryContract'
                    break
                // 体验合约
                case 2:
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/applyExperienceContractUsingPOST_1
                    api = '/contract/account/applyExperienceContract'
                    break
                // 彩金合约
                case 3:
                    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/applyBonusContractUsingPOST_1
                    api = '/contract/account/applyBonusContract'
                    break
            }

            await api_post({
                url: api,
                params: {
                    ...submitParams,
                    type: contractType,
                },
            })

            router.replace('/account')
            onSuccess(onRefresh)
        })

        return {
            title,

            initial,

            marketOptions,
            currentMarket,
            periodOptions,
            multipleOptions,
            presets,

            baseItems,
            details,
            principal,

            currentRes,
            giftAmount,
            currentMarketCurrency,

            loading,
            onSubmit,
        }
    }
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _pay: '实际付款金额',
            _protocol: '已阅读并同意{0}',
            _protocol_name: '《操盘协议》',
            _rule: '活动规则',
        },
        [LANGUAGE.zhHK]: {
            _pay: '实际付款金额',
            _protocol: '已阅读并同意{0}',
            _protocol_name: '《操盘协议》',
            _rule: '活动规则',
        },
        [LANGUAGE.enUS]: {
            _pay: 'pay',
            _protocol: 'Have read and agreed to the {0}',
            _protocol_name: '《Trading Agreement》',
            _rule: 'Activity Rule',
        },
    },
})

const {
    title,

    initial,

    marketOptions,
    currentMarket,
    periodOptions,
    multipleOptions,
    presets,

    baseItems,
    details,
    principal,

    currentRes,
    giftAmount,
    currentMarketCurrency,

    loading,
    onSubmit,
} = useLogic()

const popup = ref(false)

// 活动规则介绍
const rule = computed(() => {
    if (type.normal) {
        return ''
    } else {
        return currentMarket.value.activityRule
    }
})

defineOptions({ name: 'account-apply' })
</script>

<style scoped>

</style>
