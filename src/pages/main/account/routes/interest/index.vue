<template>
    <c-header :title="t('account.interest_records')">
        <template #right>
            <van-icon
                name="filter-o"
                @click="filterPopup = true"
            />
        </template>
    </c-header>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-2.5"
            v-for="({ fromType, createTime, beforeNum, updateNum, afterNum, currency }, i) in list"
            :data-aos-delay="50 * i"
            data-aos-anchor="#app"
            :key="i"
            :title="typeDict.get(fromType)"
            :extra="createTime"
        >
            <c-description :label="t('_before')">
                <c-amount
                    class="text-text"
                    :amount="beforeNum"
                    :currency
                />
            </c-description>

            <c-description :label="t('_change')">
                <c-amount
                    symbol
                    colorful
                    :currency
                    :amount="updateNum"
                />
            </c-description>

            <c-description :label="t('_after')">
                <c-amount :currency :amount="afterNum"/>
            </c-description>

            <!--订单号-->
            <!--<c-description :label="t('_order')">-->
            <!--    <span>{{ serialNo }}</span>-->
            <!--</c-description>-->
        </c-card>
    </c-record>

    <Filter
        v-model="filterPopup"
        @reset="onReset"
        @confirm="onRefresh"
    >
        <c-select
            class="mb-4"
            :label="t('_type')"
            popover
            :columns="options"
            :columns-field-names="{
                text: 'label',
                value: 'value'
            }"
            v-model="type"
            maxHeight="h-auto"
        />

        <!--<c-picker-->
        <!--    :title="t('form.query_time')"-->
        <!--    :options="record_range_presets"-->
        <!--    v-model:index="dateIndex"-->
        <!--/>-->
    </Filter>
</template>

<script setup>
import { record_range_presets } from '@/config/index.js'
import Filter from '@/components/Filter/index.vue'

const { idParams } = useAccountId()

const filterPopup = ref(false)

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _before: '变更前金额',
            _change: '变更金额',
            _after: '变更后金额',
            _type: '资金类型',
            _order: '流水号',
        },
        [LANGUAGE.zhHK]: {
            _before: '变更前金额',
            _change: '变更金额',
            _after: '变更后金额',
            _type: '资金类型',
            _order: '流水號',
        },
        [LANGUAGE.enUS]: {
            _before: 'Previous Amount',
            _change: 'Change Amount',
            _after: 'Post-Change Amount',
            _type: 'Fund Type',
            _order: 'Reference Number',
        },
    },
})

// 利息券类型字典表
const options = computed(() => ([
    { label: '全部', value: '' },
    { label: t('interest.l0'), value: 1 }, // 人工充值
    { label: t('interest.l1'), value: 2 }, // 签到赠送
    { label: t('interest.l2'), value: 3 }, // 活动赠送
    { label: t('interest.l3'), value: 4 }, // 申请合约账户
    { label: t('interest.l4'), value: 5 }, // 利息扣除
    { label: t('interest.l5'), value: 6 }, // 充值赠送
    { label: t('interest.l6'), value: 7 }, // 人工扣除
]))

const typeDict = utils_options_to_dict(options.value)

const type = ref(options.value[0].value),
    dateIndex = ref(-1)

const onReset = () => {
    type.value = options.value[0].value
    dateIndex.value = -1
    onRefresh()
}

const params = computed(() => {
    let createTimeStart, createTimeEnd

    if (dateIndex.value !== -1) {
        ([ createTimeStart, createTimeEnd ] = record_range_presets.value[dateIndex.value].value.map(e => e.format(TIME_FORMAT.FULL)))
    }

    return {
        ...idParams,
        fromType: type.value,
        createTimeStart,
        createTimeEnd,
    }
})

const { list, refreshLoading, loadLoading, finished, onRefresh, onLoadMore } = usePagination({
    url: '/interest/page',
    params,
})

defineOptions({ name: 'interest-records' })
</script>
