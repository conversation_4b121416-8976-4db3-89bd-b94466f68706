<!--扩大合约弹框-->
<template>
    <slot name="margin" :onMarginCallOpen="handleOpen"/>

    <van-dialog
        teleport="body"
        v-model:show="show"
        show-cancel-button
    >
        <div class="px-4 py-5">
            <div class="flex justify-center">追加保证金</div>

            <!--持仓保证金-->
            <div class="flex justify-between mt-2.5">
                <span class="text-text">持仓保证金</span>
                <c-amount color="text-primary" :amount="data?.marginAmount ?? 0"/>
            </div>

            <!--可用保证金-->
            <div class="flex justify-between mt-2.5">
                <span class="text-text">可用保证金</span>
                <c-amount color="text-primary" :amount="data?.availableMargin"/>
            </div>

            <!--可用金额-->
            <div class="flex justify-between mt-2.5">
                <span class="text-text">可用金额</span>
                <c-amount color="text-primary" :amount="$spot?.usableCash"/>
            </div>

            <!--追加金额-->
            <div class="mt-4">
                <span class="mb-2.5 inline-block">追加金额</span>
                <!--type="number" min="10" max-->
                <c-input
                    placeholder="追加金额"
                    v-model="formState.addMarginAmount"
                />
            </div>

            <!-- 操盘协议 -->
            <div
                data-aos="fade-left"
                data-aos-delay="150"
                data-aos-anchor="#app"
                class="text-xs my-4 flex-middle"
            >
                <van-checkbox
                    class="mr-2"
                    :icon-size="14"
                    v-model="protocol"
                />

                <i18n-t
                    tag="div"
                    keypath="_protocol"
                    scope="global"
                >
                <span class="text-link" @click="onShowProtocol">
                    {{ t('_protocol_name') }}
                </span>
                </i18n-t>
            </div>
            <!-- 操盘协议弹框 -->
            <ProtocolPopup/>
        </div>

        <template #footer>
            <div class="flex w-full mb-5 px-4">
                <van-button @click="show = false" class="flex-1" size="small">取消</van-button>
                &nbsp;
                &nbsp;
                <van-button class="flex-1" type="primary" size="small" @click="handleConfirm">确认</van-button>
            </div>
        </template>
    </van-dialog>
</template>

<script setup>
const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _pay: '实际付款金额',
            _protocol: '已阅读并同意{0}',
            _protocol_name: '《操盘协议》',
            _rule: '活动规则',
        },
        [LANGUAGE.zhHK]: {
            _pay: '实际付款金额',
            _protocol: '已阅读并同意{0}',
            _protocol_name: '《操盘协议》',
            _rule: '活动规则',
        },
        [LANGUAGE.enUS]: {
            _pay: 'pay',
            _protocol: 'Have read and agreed to the {0}',
            _protocol_name: '《Trading Agreement》',
            _rule: 'Activity Rule',
        },
    },
})
const show = ref(false)
const data = ref({
    marginAmount: 0,
    availableMargin: 0,
})

const accountStore = useAccountStore(),
    { $spot } = storeToRefs(accountStore)

// 协议勾选
const protocol = ref(true)

const { onShowProtocol, ProtocolPopup } = useProtocolPopup(4)
const formState = reactive({
    addMarginAmount: '',
})

const handleOpen = (v) => {
    data.value = v
    show.value = true
}

const handleConfirm = async () => {
    if (!formState.addMarginAmount) {
        return showFailToast('请输入追加金额')
    }
    try {
        const bool = await api_post({
            url: `/position/addMargin`,
            params: {
                positionId: data.value?.id,
                ...formState,
            },
        })
        if (bool) {
            showSuccessToast('追加成功')
            show.value = false
        }
    } catch (e) {
        console.log(e)
    }
}

defineOptions({
    name: 'MarginCallModal',
})
</script>
