<template>
    <AccountDetailsLayout
        :type="ACCOUNT_TYPE.SPOT"
        :interest="$spot?.interestCash"
        :total="data?.assetAmount"
        :earnings="data?.profitLoss"
        :balance="$spot.usableCash"
        :freeze="$spot.freezeCash"
        :grid
        :grid-props="{
      columnNum: 5,
    }"
        :showTitle="false"
    >
        <AccountTabs class="account-tabs-container" :type="ACCOUNT_TYPE.SPOT"/>
    </AccountDetailsLayout>
</template>

<script setup>
import { spotActiveTab } from '@/store/account.js'
import { ACCOUNT_TYPE } from '@/config/index.js'
import AccountDetailsLayout from '@/pages/main/account/components/AccountDetailsLayout.vue'
import AccountTabs from '@/pages/main/account/components/AccountTabs.vue'

const accountStore = useAccountStore(),
    { dispatch_refreshSpot } = accountStore,
    { $spot } = storeToRefs(accountStore)

const data = reactive({
    assetAmount: 0,
    profitLoss: 0,
})
dispatch_refreshSpot()

const router = useRouter()

const { t } = useI18n()

const grid = computed(() => {
    return [
        // 充值
        {
            icon: 'grid_deposit',
            title: t('account.deposit'),
            click: () => {
                sessionStorage.setItem('depositActiveTab', 'bank')
                router.push('/deposit')
            },
        },
        // 提现
        {
            icon: 'grid_withdrawal',
            title: t('account.withdrawal'),
            to: '/withdrawal',
        },
        // 交易中心
        {
            icon: 'grid_transaction',
            title: t('account.transaction'),
            click: () => {
                // 1 2 3 跳转到股票
                // 4 跳转到股指
                // 5 国内期货
                if ([ 1, 2, 3 ].includes(+spotActiveTab.value)) {
                    router.push('/quotes/stock')
                } else if (spotActiveTab.value === 4) {
                    router.push('/quotes/index')
                } else if (spotActiveTab.value === 5) {
                    router.push('/quotes/futures')
                }
            },
        },
        // 资金记录
        {
            icon: 'grid_finance',
            title: t('account.finance_record'),
            to: '/spot/financial/' + $spot.value.assetId,
        },
        // 历史订单
        {
            icon: 'grid_history',
            title: t('account.history'),
            to: `/spot/order/${$spot.value.assetId}/${spotActiveTab.value}`,
        },
    ]
})

// 监听tab变化 重新计算 持仓资产 和 浮动盈亏的值
import { watch, onUnmounted } from 'vue'

let timer = null

async function fetchData(dataType) {
    try {
        const result = await api_get({
            url: `/account/getAccountInfo`,
            params: {
                dataType,
            },
        })
        Object.assign(data, result)
    } catch (e) {
        console.log(e)
    }
}

watch(
    () => spotActiveTab.value,
    async (dataType) => {
        // 首次立即调用
        await fetchData(dataType)

        // 清除旧的定时器
        if (timer) clearInterval(timer)

        // 每 10 秒调用一次（根据你需要的时间间隔调整）
        timer = setInterval(() => {
            fetchData(dataType)
        }, 3500)
    },
    {
        immediate: true,
    },
)

onUnmounted(() => {
    // 组件卸载时清理定时器
    if (timer) clearInterval(timer)
})

// 现货账户
defineOptions({ name: 'SPOT-ACCOUNT' })
</script>

<style scoped>
.account-tabs-container {
    /*
        MarginTop 16
        总资产 100
        资产统计 64
        快捷导航 88
    */
    height: calc(100% - 16px - 100px - 64px - 88px);
}
</style>
