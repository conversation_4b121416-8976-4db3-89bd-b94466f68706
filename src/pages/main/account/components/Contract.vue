<template>
    <main
        class="custom-shadow rounded-bl-lg rounded-br-lg"
        data-aos="fade-left"
        data-aos-delay="50ms"
    >
        <!-- 总资产 -->
        <div
            class="text-white mx-4 rounded-lg overflow-hidden custom-shadow h-16 px-4 flex-between"
            :style="cardStyle"
        >
            <div>{{ t('account.total_assets') }}</div>

            <c-rate-currency
                class="text-2xl"
                :amount="$spot.accountAmount"
                v-model:currency="assetsCurrency"
                v-model:rate="assetsRate"
            />
        </div>
        <!-- 总资产 -->

        <!-- 快捷导航 -->
        <van-grid
            :column-num="contractGrid.length"
            :border="false"
            data-aos="zoom-in"
        >
            <van-grid-item
                v-for="({ title, icon, to, handler }, i) in contractGrid"
                :key="i"
                :text="title"
                :to
                @click="handler"
            >
                <template #icon>
                    <div class="w-10 h-10 flex justify-center items-center">
                        <c-icon
                            color="var(--primary)"
                            prefix="account"
                            :size="iconSize"
                            :name="icon"
                        />
                    </div>
                </template>
            </van-grid-item>
        </van-grid>
        <!-- 快捷导航 -->
    </main>

    <div class="account-tabs-container px-2.5 pt-2.5">
        <div class="h-full overflow-y-auto overflow-x-hidden">
            <c-record
                :finish="$contractFinish"
                :onRefresh="dispatch_refreshContact"
                :onLoadMore="dispatch_loadMoreContact"
                v-model:refresh-loading="$contractRefreshLoading"
                v-model:load-loading="$contractLoadLoading"
            >
                <c-card
                    class="mb-2.5"
                    v-for="({ id, marketType, type, periodType, multiple, useAmount, freezePower, todayWinAmount, todayWinRate }, i) in $contract"
                    data-aos="fade-left"
                    data-aos-anchor="#app"
                    :data-aos-delay="i * 50"
                    :key="id"
                    arrow
                    :to="`/contract/${id}`"
                >
                    <template #title>
                        <div class="flex-middle gap-2 text-sm text-title flex-1 w-1">
                            <div class="marketBlock">{{ marketType }}</div>
                            <div class="marketBlock bg-text!">{{ STOCK_CONFIG[marketType].currency }}</div>
                            <div class="truncate">
                                {{ utils_contract_name({ marketType, type, periodType, multiple, id }) }}
                            </div>
                        </div>
                    </template>

                    <c-description-group :columns="2">
                        <c-description :label="t('account.balance')">
                            <c-amount :amount="useAmount"/>
                        </c-description>
                        <c-description :label="t('account.freeze')">
                            <c-amount :amount="freezePower"/>
                        </c-description>
                        <c-description :label="t('account.today_earnings')">
                            <c-amount :amount="todayWinAmount" colorful/>
                        </c-description>
                        <c-description>
                            <c-amount
                                percent
                                colorful
                                :amount="todayWinRate"
                            />
                        </c-description>
                    </c-description-group>
                </c-card>
            </c-record>
        </div>
        <!-- 合约账户 -->
    </div>
</template>

<script setup>
import { STOCK_CONFIG } from '@/config/index.js'
import { utils_contract_name } from '@/utils'

const { t } = useI18n()
const accountStore = useAccountStore(),
    {
        dispatch_refreshSpot,
        dispatch_refreshContact,
        dispatch_loadMoreContact,
        dispatch_clearSpotIntervalFetch,
        dispatch_clearContractIntervalFetch,
    } = accountStore,
    {
        $spot,
        $spotLoading,
        $contract,
        $contractRefreshLoading,
        $contractLoadLoading,
        $contractFinish,
    } = storeToRefs(accountStore)

dispatch_refreshSpot()
dispatch_refreshContact()

onBeforeUnmount(() => {
    dispatch_clearSpotIntervalFetch()
    dispatch_clearContractIntervalFetch()
})

const iconSize = computed(() => {
    if (G_TEMPLATE === 'GP') {
        return 32
    } else if (G_TEMPLATE === 'temp_shield') {
        return 22
    }
})

// 总资产汇率
const [ assetsCurrency, assetsRate ] = useRate()

// 合约会计导航
const contractGrid = computed(() => [
    {
        icon: 'grid_apply',
        title: t('contract.title'),
        to: '/contract/apply/type/1',
    },
    // {
    //     icon: 'grid_apply_futures',
    //     title: t('futures.title'),
    //     // to: '/contract/apply/type/2',
    //     handler: () => {
    //         showFailToast(t('common.developing'))
    //     },
    // },
    {
        icon: 'grid_apply_record',
        title: t('contract.apply_record'),
        to: '/contract/apply/record',
    },
    {
        icon: 'grid_contract_history',
        title: t('contract.history'),
        to: '/contract/history',
    },
])

// 总资产部分背景色
const cardStyle = computed(() => {
    if (G_THEME === 'GP') {
        return {
            background: 'linear-gradient(90deg, #4366DE 0%, #3150BD 100%)',
        }
    } else if (G_THEME === 'golden') {
        return {
            background: 'var(--btn-bg)',
        }
    } else if (G_THEME === 'lightBlue') {
        return {
            background: 'var(--btn-bg)',
        }
    } else if (G_THEME === 'yellow') {
        return {
            background: 'var(--btn-bg)',
        }
    }
})

defineOptions({
    title: 'Contract Account',
})
</script>

<style scoped>
.card-footer {
    background: rgba(255, 255, 255, .1);
}

.van-theme-dark main {
    background: rgba(255, 255, 255, .04);
}

.van-grid {
    --van-grid-item-content-padding: 16px 0;
    --van-grid-item-text-color: var(--title)
}

:deep(.van-grid-item__text) {
    margin-top: 4px;
}

.account-tabs-container {
    /*
        总资产 64
        快捷导航 88
    */
    height: calc(100% - 64px - 88px);
}
</style>
