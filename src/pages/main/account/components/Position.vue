<template>
    <div class="position bg-bg p-2.5">
        <div @click="_onClick">
            <div class="flex-middle gap-2 mb-2.5">
                <!-- 交易类型 -->
                <div class="marketBlock" :style="{ backgroundColor: `var(--${tradeTypeConfig?.color})` }">
                    {{ t(`dict.${tradeTypeConfig?.label}`) }}
                </div>
                <!-- 交易类型 -->

                <div class="marketBlock bg-raise!">
                    {{ t('dict.开') }}
                </div>

                <!-- 股票市场 -->
                <div class="marketBlock">
                    <!--{{ stockMarketDict.get(data.market) }}-->
                    {{ data?.market }}
                </div>
                <!-- 股票市场 -->

                <!-- 股票名称 -->
                <div class="text-title text-sm font-semibold">
                    {{ data?.symbolName }}
                </div>
                <!-- 股票名称 -->

                <!-- 币种 -->
                <div class="text-xs">
                    <!--({{ STOCK_CONFIG[stockCountryDict.get(data.market)]?.currency }})-->
                    ({{ data?.currency }})
                </div>
                <!-- 币种 -->

                <div class="text-xs">
                    ({{ data?.id }})
                </div>
            </div>

            <!-- 持仓详情 -->
            <div class="grid grid-cols-3">
                <div
                    v-for="({ title, key, symbol, colorful, precision }) in renderConfig"
                    :key
                >
                    <div class="text-text text-xs">{{ title }}</div>
                    <c-amount
                        class="text-primary"
                        :amount="data[key]"
                        :symbol
                        :colorful
                        :precision
                    />
                </div>
            </div>
            <!-- 持仓详情 -->
        </div>

        <!-- 国内期货(spotActiveTab === 5) tab下多出三个按钮[止盈止损/追加/详情] -->
        <div v-if="isFutures" class="flex justify-between mt-2.5">
            <!--止盈止损-->
            <TackProfitStopLoss>
                <template v-slot:trigger="{ onOpen }">
                    <van-button @click="onOpen(data)" type="primary" size="mini">止盈止损</van-button>
                </template>
            </TackProfitStopLoss>
            <!--止盈止损-->
            <MarginCallModal>
                <template #margin="{ onMarginCallOpen }">
                    <van-button @click="onMarginCallOpen(data)" type="primary" size="mini">追加</van-button>
                </template>
            </MarginCallModal>
            <!-- 跳转到交易详情页 -->
            <van-button type="primary" @click.self="handleGoDetail($event, data)" size="mini">详情</van-button>
        </div>
    </div>

</template>

<script setup>
import { spotActiveTab } from '@/store/account.js'
import { accountActiveTab } from '@/store'
import TackProfitStopLoss from './takeProfitStopLoss.vue'
import MarginCallModal from './marginCallModal.vue'
import { useFuturesStore } from '@/store/futures.js'

const route = useRoute()

const isFutures = computed(() => {
    // /futures/transaction
    return (spotActiveTab.value === 5 && accountActiveTab.value === 'spot' && route.path === '/account') || route.path === '/futures/transaction'
})
const { data, contractId, onClick } = defineProps({
    // 持仓数据
    data: {
        type: Object,
        required: true,
    },
    // 合约ID
    contractId: [ String, Number ],
    onClick: Function,
})
const router = useRouter()
const { dispatch_checkStock } = useStockStore()
const futuresStore = useFuturesStore()
const { dispatch_checkFutures } = futuresStore
const { $currentFutures, $futuresActiveTab } = storeToRefs(futuresStore)

const _onClick = () => {
    // 默认跳转股票交易页
    if (isFutures.value) {
        $currentFutures.value = data
        // 期货
        onClick ? onClick(data) : dispatch_checkFutures(data, { route: 'transaction' })
        $futuresActiveTab.value = 'futures-details'
    } else {
        // 股指
        onClick ? onClick(data) : dispatch_checkStock(data, { route: 'transaction', params: { contractId } })
    }
    // 区分股指和期货
}

const handleGoDetail = (e, v) => {
    e.stopPropagation()
    e.preventDefault()
    router.push(`/transaction/detail/${v.id}`)
}

const tradeTypeConfig = computed(() => tradeTypeDict(data.tradeType))

const { t } = useI18n()

// 持仓详情渲染配置
const renderConfig = [
    { title: t('stock.position.available'), key: 'restNum' },
    { title: t('stock.current_price'), key: 'stockPrice', precision: 3 },
    {
        title: t('account.floatingProfitAndLoss'),
        key: 'floatingProfitLoss',
        symbol: true,
        colorful: true,
    },
    { title: t('stock.position.total'), key: 'positionTotalNum' },
    { title: t('stock.position.average'), key: 'buyAvgPrice', precision: 3 },
    // availableMargin
    {
        title: isFutures.value ? t('futures.availableMargin') : t('stock.market_value'),
        key: isFutures.value ? "availableMargin" : 'marketValue'
    },
]

// 当前持仓组件
defineOptions({ name: 'Position' })
</script>

<style scoped>
.position:not(:last-of-type) {
    border-bottom: 1px solid var(--border);
}

.grid > div:nth-child(3n) {
    text-align: right;
}

.grid > div:nth-child(3n - 1) {
    text-align: center;
}
</style>
