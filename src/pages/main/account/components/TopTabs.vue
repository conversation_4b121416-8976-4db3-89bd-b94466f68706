<template>
    <div class="px-2.5 pt-2.5 mb-1.25">
        <!-- Use SkewTabs when isTempCoin is true, otherwise use van-tabs -->
        <SkewTabs
            v-if="isTempCoin"
            v-model="accountActiveTab"
            :tabs="skewTabsData"
            :is-temp-coin="true"
            @change="handleSkewTabChange"
        />

        <van-tabs
            v-else
            class="main-tab"
            type="card"
            title-inactive-color="var(--paragraph)"
            shrink
            v-model:active="accountActiveTab"
        >
            <van-tab
                v-for="({title, key, name}, idx) in accountTabs"
                :key="key"
                :title="title"
                :name="name"
            />
        </van-tabs>

        <van-tabs v-if="accountActiveTab === 'spot'" v-model:active="spotActiveTab">
            <van-tab v-for="({title, key, name}) in tabs" :key :title :name/>
        </van-tabs>
    </div>
</template>

<script setup>
const { t } = useI18n()
import { spotActiveTab } from '@/store/account.js'
import { accountActiveTab } from '@/store'
import socket from '@/socket.js'
import SkewTabs from '@/components/SkewTabs/index.vue'

const showTabs = ref(null)

const isTempCoin = computed(() => {
    return G_TEMPLATE === 'temp_coin'
})

const accountTabs = computed(() => {
    return [
        {
            title: t('account.spot'),
            key: 'x_001',
            name: 'spot',
        },
        {
            title: t('account.contract'),
            key: 'x_002',
            name: 'contract',
        },
    ]
})

// SkewTabs data format
const skewTabsData = computed(() => {
    return [
        {
            label: t('account.spot'),
            value: 'spot',
            key: 'x_001',
        },
        {
            label: t('account.contract'),
            value: 'contract',
            key: 'x_002',
        },
    ]
})

// Handle SkewTabs change
const handleSkewTabChange = (tab) => {
    accountActiveTab.value = tab.value
}

const handleGet = async () => {
    showTabs.value = await api_get({
        url: '/site/getMarketOpenAsset',
    })
}

handleGet()

// System Config Store
const sysConfigStore = useSysConfigStore()
const { showIndexSection, showFuturesSection, $sysConfig } = storeToRefs(sysConfigStore)

socket.on('assetDataTypeUpdate', handleGet)
// 数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
const tabs = computed(() => {
    const list1 = [
        {
            key: 'x_001',
            title: 'A股',
            name: 1,
        },
        {
            key: 'x_002',
            title: '港股',
            name: 2,
        },
        {
            key: 'x_003',
            title: '美股',
            name: 3,
        },
        {
            key: 'x_004',
            title: '股指',
            name: 4,
        },
    ]

    const list2 = [
        {
            key: 'x_005',
            title: '国内期货',
            name: 5,
        },
    ]

    const list3 = [ ...list1, ...list2 ]

    const list = $sysConfig.value.tradingMode === 1 ? list1 : $sysConfig.value.tradingMode === 2 ? list2 : list3

    if (showIndexSection.value) {
        return list.filter(item => {
            return showTabs.value?.includes(item.name)
        })
    } else {
        return list
    }

})

defineProps({
    title: 'Account-Top-Tabs',
})
</script>

<style scoped>
:deep(.main-tab > .van-tabs__wrap) {
    background: var(--card);
    border-radius: var(--van-tabs__wrap);
}

:deep(.main-tab .van-tabs__nav--card) {
    display: flex;
    margin: 0;
    border-radius: var(--van-tabs__nav--card);
    border: none;
    justify-content: space-between;
}


:deep(.main-tab .van-tabs__nav--card > .van-tab) {
    height: 80%;
    flex: 1;
    border: none;
    border-radius: var(--van-tab);
}
</style>
