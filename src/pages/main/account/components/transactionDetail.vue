<template>
    <c-header title="交易详情页"/>

    <div class="with-header-container">
        <c-card>
            <c-description-group>
                <template #prefix>
                    <div class="text-sm flex justify-between">
                        <div>
                            <span class="text-primary mr-1.25">{{ res?.symbolName }}</span>
                            <span class="bg-tag text-primary">{{ res?.market }}</span>
                        </div>

                        <van-tag v-if="+res?.tradeType === 1" color="#C92C31">开多</van-tag>
                        <van-tag color="#2cb274" v-else>平空</van-tag>
                    </div>
                    <p class="text-sm mt-2.5">持仓盈亏({{ res?.currency }})</p>
                    <!-- 字体颜色 -->
                    <div
                        :class="[utils_amount_color(res?.floatingProfitLoss)]"
                    >
                        <c-amount :animate="false" :amount="res?.floatingProfitLoss" class="text-2xl"/>
                    </div>

                    <div
                        :class="[utils_amount_color(res?.floatingProfitLoss)]"
                    >
                        <c-amount :amount="res?.floatingProfitLossRate ?? 0" percent/>
                    </div>
                </template>
            </c-description-group>
        </c-card>

        <c-card
            data-aos-delay="50"
            class="mt-2.5"
        >
            <c-description-group
                :items="details"
                :data-source="res"
            />
        </c-card>
    </div>
</template>

<script setup lang="jsx">
import { utils_amount_color } from '@/utils/index.js'
import { tradeTypeDict } from '@/config/stock.js'

const { params: { positionId } } = useRoute()
const { res, initial } = useRequest({
    url: `/position/get/${positionId}`,
    initialValues: {
        cancelTime: '',
        currency: '',
        dealNum: 0,
        dealPrice: 0,
        dealTime: '',
        direction: 0,
        id: 0,
        market: '',
        priceType: 0,
        securityType: '',
        status: 0,
        stockPrice: 0,
        symbol: '',
        symbolName: '',
        tradeNum: 0,
        tradePrice: 0,
        tradeTime: '',
        tradeType: 0,
        transactionAmount: 0,
        type: 0,
        costPrice: 0,
        winAmount: 0,
        tradeFee: 0,
        contractId: 0,
        contractType: 0,
        multiple: 0,
        periodType: 0,
        tradeRate: 0,
    },
})

const details = computed(() => [
    // 订单号
    {
        label: '订单号',
        value: 'orderNo',
    },
    // 订单类型
    {
        label: t('stock.entrust.type'),
        value: 'tradeType',
        render: val => {
            if (!val) return '-'
            return <span className={`text-${tradeTypeDict(val)?.color}`}> {tradeTypeDict(val)?.label} </span>
        },
    },

    {
        label: '成交价格', value: 'buyAvgPrice', render: funcRender,
    },
    {
        label: '持仓数量', value: 'positionTotalNum', render: funcRender,
    },
    { label: '可卖', value: 'restNum', render: funcRender },
    { label: '手续费', value: 'feeAmount', render: funcRender },
    {
        label: '持有天数', value: 'positionDays',
    },
    {
        label: '保证金比例', value: 'marginRatio', render: (val) => {
            return <c-amount amount={val} percent/>
        },
    },
    { label: '保证金金额', value: 'marginAmount', render: funcRender },
    {
        label: '保证金预警线', value: 'warningLine', render: (val) => {
            return <c-amount color="text-red-text" amount={val}/>
        },
    },
    { label: '保证金强平线', value: 'closeLine', render: funcRender },
    { label: '追加保证金', value: 'appendMargin', render: funcRender },
    { label: '止盈价格', value: 'takeProfitValue', render: funcRender },
    { label: '止损价格', value: 'stopLossValue', render: funcRender },
    { label: '距离预警线金额', value: 'distanceWarningLine', render: funcRender },
    { label: '距离强平线金额', value: 'distanceCloseLine', render: funcRender },
])

const funcRender = val => {
    return <c-amount amount={val}/>
}

const { t } = useI18n()

defineOptions({ name: 'entrust-details' })
</script>
