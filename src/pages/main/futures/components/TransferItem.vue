<template>
    <div class="h-6 flex-between text-xs px-3">
        <div class="size-4 flex-center rounded-xs text-white" :class="serialBg">
            {{ serial }}
        </div>

        <c-amount
            class="flex-1 justify-end"
            :precision="3"
            :amount="price"
        />

        <slot/>
    </div>
</template>

<script setup>
defineProps({
    serial: {
        type: Number,
        required: true,
    },
    serialBg: {
        required: true,
    },
    price: {
        type: Number,
        required: true,
    },
})

defineOptions({ name: 'TransferItem' })
</script>

<style scoped>

</style>
