<template>
    <div :class="$raise_fall.color">
        <!-- 最新价 -->
        <!--:currency="isStockIndex ? $currentFutures.currency: ''"-->
        <!--:currency="isStockIndex ? $currentFutures.currency: ''"-->
        <!--:currency="isStockIndex ? $currentFutures.currency: ''"-->
        <!--:currency="isStockIndex ? $currentFutures.currency: ''"-->
        <c-amount
            :precision="2"
            :amount="$currentFutures.latestPrice"
            :animate="false"
            font-size="text-2xl"
        />
        <!-- 最新价 -->

        <van-icon
            v-if="$currentFutures.gain"
            name="play"
            class="ml-2"
            :size="18"
            :class="[ $raise_fall.raise ? '-rotate-90' : 'rotate-90' ]"
        />

        <div class="text-xs">
            <c-amount
                symbol
                :precision="3"
                :amount="$currentFutures.chg"
                :animate="false"
            />
            <c-amount
                symbol
                percent
                :precision="3"
                :amount="$currentFutures.gain * 100"
                :animate="false"
                class="ml-2"
            />
        </div>
    </div>
</template>

<script setup>
const futuresStore = useFuturesStore()

const { $currentFutures, $raise_fall } = storeToRefs(futuresStore)
const { dispatch_get_futures_detail } = futuresStore

const intervalId = setInterval(() => {
    dispatch_get_futures_detail()
}, 3500)

onBeforeUnmount(() => {
    clearInterval(intervalId)
})

defineOptions({ name: 'FuturesPrice' })
</script>
