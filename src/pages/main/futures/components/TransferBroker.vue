<template>
    <div class="bg-bg p-4" v-if="buy.length && sell.length">
        <div class="text-sm mb-2.5">
            <div class="flex-middle">
                <span class="text-fall flex-1">{{ t('_sell') }}</span>

                <div class="flex-between flex-1">
                    <span class="text-raise">{{ t('_buy') }}</span>

                    <van-popover
                        v-model:show="countPopover"
                        placement="left-start"
                    >
                        <div class="bg-bg p-2.5 flex flex-col gap-2.5">
                            <div
                                class="block"
                                v-for="n in counts"
                                :key="n"
                                @click="count = n; countPopover = false;"
                            >
                                {{ n }}
                            </div>
                        </div>

                        <template #reference>
                            <div class="block">
                                {{ count }}
                            </div>
                        </template>
                    </van-popover>
                </div>
            </div>
        </div>

        <c-collapse
            :tip="false"
            :arrow="false"
            :fold="count * 24"
            :unfold="count * 24"
            disabled
        >
            <div class="grid grid-cols-2">
                <Render type="sell"/>
                <Render type="buy"/>
            </div>
        </c-collapse>
    </div>
</template>

<script setup>
import Amount from '@/components/Amount/index.vue'
import TransferItem from './TransferItem.vue'
import { useTransferRenderConfig } from '@/pages/main/stock/components/hooks.js'

const { buy, sell } = defineProps({
    buy: {
        type: Array,
        required: true,
    },
    sell: {
        type: Array,
        required: true,
    },
})

const counts = [ 10, 20, 40 ],
    count = ref(10),
    countPopover = ref(false)

const Render = ({ type }) => {
    const { itemBg, firstItemBg, serialBg, textColor } = useTransferRenderConfig(type)

    const data = type === 'buy' ? buy : sell

    return h(
        'div',
        data.map(e => {
            const { level, num, price, list } = e

            return h(
                'div',
                [
                    h(
                        TransferItem,
                        {
                            class: [ textColor, firstItemBg ],
                            serialBg,
                            serial: level,
                            price,
                        },
                        () => h(
                            Amount,
                            {
                                class: 'flex-1 justify-end text-title',
                                precision: 0,
                                amount: num,
                            },
                        ),
                    ),
                    list.map(l => {
                        const { brokerId, brokerName } = l

                        return h(
                            'div',
                            {
                                class: [ 'h-6 px-3 flex-middle gap-2.5 text-title text-sm', itemBg ],
                            },
                            [
                                h('span', brokerId),
                                h('span', brokerName),
                            ],
                        )
                    }),
                ],
            )
        }),
    )
}

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _buy: '买盘经纪',
            _sell: '卖盘经纪',
        },
        [LANGUAGE.zhHK]: {
            _buy: '买盘经纪',
            _sell: '卖盘经纪',
        },
        [LANGUAGE.enUS]: {
            _buy: 'Buy Broker',
            _sell: 'Sell Broker',
        },
    },
})

// 股票交易百分比和体量
defineOptions({ name: 'TransferBroker' })
</script>

<style scoped>
.block {
    color: var(--title);
    font-size: 12px;
    border: 1px solid currentColor;
    width: 20px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    border-radius: 6px;
}
</style>
