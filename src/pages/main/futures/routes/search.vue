<template>
    <div class="h-(--header-height) flex-between gap-2.5 px-4">
        <Search
            class="flex-1"
            v-model="content"
            @search="onSearch"
        />

        <div class="text-link" @click="$router.back">
            {{ $t('operation.cancel') }}
        </div>
    </div>

    <div class="with-header-container flex flex-col gap-2">
        <div v-show="!content">
            {{ t('history') }}
        </div>

        <div :class="{ 'history-container': !content }">
            <template v-if="!renderList.length && content">
                <van-empty :description="$t('common.empty')"/>
            </template>

            <template v-else>
                <div
                    class="py-2 flex-between gap-4 not-last:border-b border-border"
                    v-for="item in renderList"
                    :key="item.symbol"
                >
                    <StockBaseInfo
                        class="flex-1"
                        :name="item.name"
                        :market="item.market"
                        :symbol="item.symbol"
                        @click="onCheck(item)"
                    />

                    <van-icon
                        name="cross"
                        v-show="!content"
                        @click="onDelete(item.symbol)"
                    />
                </div>
            </template>
        </div>

        <div
            class="text-center text-link text-xs"
            v-show="!content"
            @click="history = [];"
        >
            {{ t('clear') }}
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash'
import Search from '@/components/Search/index.vue'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'
import { STOCK_ROUTE } from '@/config/index.js'

const route = useRoute()
const { $stockActiveTab } = storeToRefs(useStockStore())
const { dispatch_checkStock } = useStockStore()

const content = ref(''),
    result = ref([])

const history = useSessionStorage('searchHistory', [])
const renderList = computed(() => content.value ? result.value : history.value)
const [ onSearch ] = useFetchLoading(async (keyword) => {
    result.value = await api_get({
        url: '/symbol/search',
        params: {
            keyword,
        },
        options: {
            headers: {
                keepLatest: true,
            },
        },
    })
}, { keepLatest: true })

const onCheck = (stock) => {
    history.value = _.uniqBy([ stock, ...history.value ], 'symbol')
    if (route.params.globalSearch) {
        // 顶部搜索走这个逻辑
        dispatch_checkStock(stock, { isReplace: true })
    } else {
        // 详情页里的搜索走这个逻辑
        $stockActiveTab.value = STOCK_ROUTE.TRANSACTION
        dispatch_checkStock(stock, { isBack: true })
    }
}

const onDelete = symbol => {
    history.value = _.filter(history.value, e => e.symbol !== symbol)
}

useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%82%A1%E7%A5%A8%E9%85%8D%E7%BD%AE%E7%9B%B8%E5%85%B3/getBlacklistUsingGET_1
    url: '/symbol/blacklist',
    onSuccess: res => {
        // 过滤黑名单不展示
        history.value = _.filter(history.value, e => !res.includes(e.instrument))
    },
    onErr: () => {
        history.value = []
    },
})

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            history: '历史搜索记录',
            clear: '清空搜索记录',
        },
        [LANGUAGE.zhHK]: {
            history: '历史搜索记录',
            clear: '清空搜索记录',
        },
        [LANGUAGE.enUS]: {
            history: 'History',
            clear: 'Clear',
        },
    },
})

defineOptions({ name: 'stock-search' })
</script>

<style scoped>
.history-container {
    /*
        历史搜索记录 24
        清空搜索记录 16
        Gap 8
    */
    max-height: calc(100% - 24px - 16px - 8px);
}
</style>
