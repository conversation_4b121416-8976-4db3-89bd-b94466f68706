<template>
    <van-tabs
        class="stock-tabs h-full"
        v-model:active="tabActive"
    >
        <van-tab name="quotes" :title="t('stock.quotes')">
            <!-- 行情 -->
            <QuotesTab/>
        </van-tab>

        <van-tab
            name="introduction"
            :title="t('_introduction')"
        >
            <!-- 简况 -->
            <IntroductionTab/>
        </van-tab>
    </van-tabs>
</template>

<script setup>
import QuotesTab from './components/QuotesTab.vue'
import IntroductionTab from './components/IntroductionTab.vue'

const { $currentStock, $stock } = storeToRefs(useStockStore())

const tabActive = useSessionStorage('futuresDetailsTab', 'quotes')

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _introduction: '简况',
            _financial: '资金',
        },
        [LANGUAGE.zhHK]: {
            _introduction: '简况',
            _financial: '资金',
        },
        [LANGUAGE.enUS]: {
            _introduction: 'Introduction',
            _financial: 'Finance',
        },
    },
})

defineOptions({ name: 'stock-details' })
</script>

<style scoped>
</style>
