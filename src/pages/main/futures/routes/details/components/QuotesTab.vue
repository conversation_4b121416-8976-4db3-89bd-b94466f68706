<template>
    <div style="height: calc(100% - 56px);" class="overflow-y-auto">
        <div class="bg-bg px-4">
            <c-collapse
                :fold="105"
                :unfold="135"
            >
                <div class="flex-between mt-2.5">
                    <div>
                        <!--开盘状态-->
                        <div class="text-title">
                            {{ futuresStatus?.statusStr }}
                        </div>

                        <FuturesPrice/>
                    </div>
                    <div class="ml-auto flex-1 flex-middle gap-2">
                        <div class="flex-1 w-1 text-right text-red">{{ $currentFutures?.name }}</div>
                        <!--国旗-->
                        <c-icon :name="stockCountryDict.get('SZSE')"/>
                    </div>

                </div>

                <c-description-group
                    class="gap-x-4"
                    label-class="text-xs"
                    value-class="text-xs"
                    :columns="detailsColumns"
                    :items="details"
                >
                    <template #template="{ value, color, percent, suffix, precision }">
                        <c-amount
                            :class="[ color || 'text-title' ]"
                            :amount="+value"
                            :percent
                            :precision="!percent ? (precision ?? $futures.precision) : 2"
                            :suffix
                            :animate="false"
                            big-unit
                        />
                    </template>
                </c-description-group>
            </c-collapse>
        </div>

        <div class="my-2.5 bg-bg px-2.5 pb-2.5">
            <!-- 分时/5日/日k/周k/月k/年k/1分/5分/15分/30分 -->
            <van-tabs
                class="mb-2.5"
                v-model:active="chartActiveTab"
                @change="onGetChartData"
            >
                <!--:disabled="chartInitial && chartLoading"-->
                <van-tab
                    v-for="key in baseCharts"
                    :key
                    :name="key"
                    :title="t(key)"
                    @click="handleTabClick(key)"
                />

                <van-tab :disabled="chartLoading" :name="minuteType">
                    <template #title>
                        <van-dropdown-menu trigger="click">
                            <van-dropdown-item
                                v-model="minuteType"
                                :options="minuteOptions"
                                :disabled="chartLoading"
                                @change="onGetChartData"
                            />
                        </van-dropdown-menu>
                    </template>
                </van-tab>
            </van-tabs>

            <!-- 走势图 -->
            <div class="flex gap-1">
                <div class="flex-1 w-1 relative h-[300px]">
                    <van-skeleton :loading="!chartInitial">
                        <template #template>
                            <van-empty class="w-full" :description="$t('common.empty')"/>
                        </template>

                        <c-echarts
                            class="border border-border"
                            :option
                        />

                        <template v-if="chartInitial">
                            <div class="chartInfo top-1 flex-middle flex-wrap gap-1 w-4/5">
                                <div
                                    v-for="({ title, key, percent, color, colorful, bigUnit, symbol, precision: _precision }) in currentChartType.axisInfoConfig"
                                    :key
                                >
                                    {{ title }}:
                                    <c-amount
                                        :style="{ color }"
                                        :class="{ [currentAxisInfo.color]: colorful }"
                                        :amount="currentAxisInfo[key]"
                                        :percent
                                        :bigUnit
                                        :symbol
                                        :precision="_precision || precision"
                                    />
                                </div>
                            </div>

                            <div class="chartInfo left-1 top-[190px]">
                                VOL {{ vol }}
                            </div>
                        </template>
                    </van-skeleton>
                </div>

                <!-- 股票分时交易体量 -->
                <div
                    v-show="chartActiveTab === baseCharts[0]"
                    class="w-[100px] h-[300px] overflow-auto border border-border text-[10px] text-paragraph whitespace-nowrap px-1 leading-4"
                >
                    <c-record
                        v-if="tradeQuotes.length"
                        :finished
                        :onRefresh
                        :onLoadMore
                        v-model:refresh-loading="refreshLoading"
                        v-model:load-loading="loadLoading"
                    >
                        <div
                            class="flex-between"
                            v-for="({ tradeTime, direction, tradePrice, tradeVolume }) in tradeQuotes"
                            :key="tradeTime"
                        >
                            <!--<div>-->
                            <!--    {{ utils_time(tradeTime * 1000, TIME_FORMAT.Hm) }}-->
                            <!--</div>-->

                            <div
                                class="text-center"
                                :class="[ tradePrice > $currentFutures.close ? 'text-raise' : 'text-fall' ]"
                            >
                                {{ utils_currency(tradePrice, { precision: $currentFutures?.precision }) }}
                            </div>

                            <div class="text-right">
                                {{ utils_big_amount_unit(tradeVolume, { config: { precision: $currentFutures.precision } }) }}
                                <span
                                    :class="{
                                        'text-raise': direction === 'B',
                                        'text-fall': direction === 'S',
                                        'text-text': direction === 'N',
                                    }"
                                >
                                    {{ direction }}
                                </span>
                            </div>
                        </div>
                    </c-record>
                </div>
                <!-- 股票分时交易体量 -->
            </div>
            <!-- 走势图 -->

        </div>

        <!-- 交易体量 -->
        <TransferVolume class="bg-bg p-4"/>
        <!-- 交易体量 -->
    </div>

    <!-- 期货的交易预警 -->
    <div class="absolute bottom-0 bg-bg w-full flex-middle h-14 rounded-tr-md rounded-tl-md bottom-shadow">
        <div class="w-1/2 flex-middle justify-evenly text-xs text-center">
            <router-link
                :to="{ path: '/stock/warning/add', query: { market, securityType, symbol } }"
            >
                <van-icon
                    size="20"
                    name="bell"
                />
                <div>{{ t('_warning') }}</div>
            </router-link>
            <div :class="{ 'text-primary': collectRes?.id }" @click="onCollect">
                <van-icon
                    size="20"
                    name="like"
                />
                <div>{{ t('stock.collect') }}</div>
            </div>
        </div>
        <!-- 底部交易按钮 -->
        <div class="w-1/2 flex-center">
            <van-button
                type="primary"
                size="small"
                class="w-[120px]"
                @click="handleGo"
            >
                {{ t('stock.transition') }}
            </van-button>
        </div>
    </div>
    <!-- 期货的交易预警 -->
</template>

<script setup>
import _ from 'lodash'
import * as echarts from 'echarts'

import { CHART_TYPES, STOCK_ROUTE, stockCountryDict, TIME_FORMAT } from '@/config'
import { utils_time, utils_big_amount_unit, utils_currency } from '@/utils/index.js'
import TransferVolume from '../../../components/TransferVolume.vue'
import socket from '@/socket.js'
import FuturesPrice from '@/pages/main/futures/components/futuresPrice.vue'
import { useFuturesStatus } from '@/hooks/futures.js'
import { useFuturesRedirect } from '@/hooks/index.js'

// 期货交易状态
const { futuresStatus } = useFuturesStatus()
const currentLang = useCurrentLang()
const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _warning: '预警',
            _highest: '最高',
            _today_open: '今开',
            _deal_amount: '成交额',
            _lowest: '最低',
            _yesterday_close: '昨收',
            _deal_volume: '成交量',
            _turnover: '换手率',
            _ttm: '市盈率TTM',
            _capital: '总股本',
            _52_highest: '52周最高',
            _ratio_static: '市盈率(静)',
            _amplitude: '振幅',
            _52_lowest: '52周最低',
            _ratio_dynamic: '市盈率(动)',
            _dividends: '股息',
            _dividends_rate: '股息率',
            _market_ratio: '市净率',
            _per: '每手',
            _avg: '均价',
            _open: '开',
            _high: '开',
            _low: '开',
            _close: '开',
            realtime_day: '分时',
            realtime_5day: '5日',
            kline_day: '日K',
            kline_week: '周K',
            kline_month: '月K',
            kline_year: '年K',
            kline_1min: '1分',
            kline_5min: '5分',
            kline_15min: '15分',
            kline_30min: '30分',
        },
        [LANGUAGE.zhHK]: {
            _warning: '预警',
            _highest: '最高',
            _today_open: '今开',
            _deal_amount: '成交额',
            _lowest: '最低',
            _yesterday_close: '昨收',
            _deal_volume: '成交量',
            _turnover: '换手率',
            _ttm: '市盈率TTM',
            _capital: '总股本',
            _52_highest: '52周最高',
            _ratio_static: '市盈率(静)',
            _amplitude: '振幅',
            _52_lowest: '52周最低',
            _ratio_dynamic: '市盈率(动)',
            _dividends: '股息',
            _dividends_rate: '股息率',
            _market_ratio: '市净率',
            _per: '每手',
            _avg: '均价',
            _open: '开',
            _high: '开',
            _low: '开',
            _close: '开',
            realtime_day: '分时',
            realtime_5day: '5日',
            kline_day: '日K',
            kline_week: '周K',
            kline_month: '月K',
            kline_year: '年K',
            kline_1min: '1分',
            kline_5min: '5分',
            kline_15min: '15分',
            kline_30min: '30分',
        },
        [LANGUAGE.enUS]: {
            _warning: 'Warning',
            _highest: 'Highest',
            _today_open: 'Today\'s Open',
            _deal_amount: 'Trade Amount',
            _lowest: 'Lowest',
            _yesterday_close: 'Yesterday\'s Close',
            _deal_volume: 'Trade Volume',
            _turnover: 'Turnover Rate',
            _ttm: 'TTM P/E Ratio',
            _capital: 'Total Shares',
            _52_highest: '52-Week High',
            _ratio_static: 'Static P/E Ratio',
            _amplitude: 'Amplitude',
            _52_lowest: '52-Week Low',
            _ratio_dynamic: 'Dynamic P/E Ratio',
            _dividends: 'Dividends',
            _dividends_rate: 'Dividend Yield',
            _market_ratio: 'Price-to-Book Ratio',
            _per: 'Per Share',
            _avg: 'Average Price',
            _open: 'Open',
            _high: 'High',
            _low: 'Low',
            _close: 'Close',
            realtime_day: 'Intraday',
            realtime_5day: '5-Day',
            kline_day: 'Daily K-Line',
            kline_week: 'Weekly K-Line',
            kline_month: 'Monthly K-Line',
            kline_year: 'Yearly K-Line',
            kline_1min: '1-Minute',
            kline_5min: '5-Minute',
            kline_15min: '15-Minute',
            kline_30min: '30-Minute',
        },
    },
})
let timer = null
const { onRedirect } = useFuturesRedirect()

const futuresStore = useFuturesStore()
const { $currentFutures, $futuresActiveTab, $minuteType, $futures, $futuresInstrument, $volume } = storeToRefs(futuresStore),
    { market, securityType, symbol } = $currentFutures.value

const handleGo = () => {
    onRedirect('futures-transaction')
    $futuresActiveTab.value = 'futures-transaction'
}
const { res: collectRes, onRefresh: onRefreshCollectStatus } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getUserOptionalBySymbolUsingGET_1
    url: '/symbol/watchlist/getBySymbol',
    params: {
        securityType,
        market,
        symbol,
    },
    initialValues: {
        id: 0,
    },
    needLogin: true,
    formatResult: res => res ?? { id: 0 },
})

const [ onCollect ] = useFetchLoading(async () => {
    if (collectRes.value?.id) {
        await api_delete({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/deleteUserOptionalUsingDELETE_1
            url: '/symbol/watchlist/delete',
            params: {
                id: collectRes.value?.id,
            },
        })
    } else {
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/addUserOptionalUsingPOST_1
            url: '/symbol/watchlist/add',
            params: {
                market,
                securityType,
                symbol,
            },
        })
    }

    onRefreshCollectStatus()

    showSuccessToast(t('operation.successfully'))
})

// 颜色色值
const [ primaryColor, raiseColor, fallColor, flatColor ] = useCssVariable([ 'primary', 'raise', 'fall', 'text' ]),
    colors = [
        // AVG, MA5
        '#F6A445',
        // MA10'
        '#9066B9',
        // MA30'
        '#3997ED',
        // MA60
        '#D83D73',
    ]

const detailsColumns = computed(() => currentLang.value.isEN ? 2 : 3)
// 详情：从左到右、从上往下排序
const details = computed(() => {
    const {
        high,
        open,
        amount,
        low,
        close,
        volume,
    } = $currentFutures.value

    return [
        {
            label: '最高价',
            value: high,
            color: utils_amount_color(high - close),
            precision: 3,
        },
        {
            label: '最低价',
            value: low,
            color: utils_amount_color(open - close),
            precision: 3,
        },
        { label: '开盘价', value: open, color: utils_amount_color(open - close) },
        {
            label: '昨收价',
            value: close,
            precision: 3,
        },
        { label: '成交额', value: amount, precision: 3 },
        { label: '成交量', value: volume },
    ]
})

// 当前轴线信息
const currentAxisInfo = ref({
        price: 0,
        change: 0,
        percent: 0,
        deal: 0,
        volume: 0,
        avg: 0,
        color: '',
        open: 0,
        high: 0,
        low: 0,
        close: 0,
        ma5: 0,
        ma10: 0,
        ma30: 0,
        ma60: 0,
    }),
    vol = computed(() => currentAxisInfo.value.volume ? utils_big_amount_unit(currentAxisInfo.value.volume) : '--')

// 分时图窗口信息
const realtimeAxisInfoConfig = computed(() => [
    { title: t('stock.transaction.price'), key: 'price', colorful: true },
    { title: t('stock.change'), key: 'change', colorful: true, symbol: true },
    { title: t('stock.change_percent'), key: 'percent', percent: true, colorful: true },
    { title: t('_deal_amount'), key: 'deal', bigUnit: true },
    { title: t('_deal_volume'), key: 'volume', bigUnit: true },
    { title: t('_avg'), key: 'avg', color: colors[0], precision: 3 },
])

// K线图窗口信息
const klineAxisInfoConfig = computed(() => [
    { title: t('_open'), key: 'open', colorful: true },
    { title: t('_high'), key: 'high', colorful: true },
    { title: t('_low'), key: 'low', colorful: true },
    { title: t('_close'), key: 'close', colorful: true },
    { title: t('_deal_amount'), key: 'deal', bigUnit: true },
    { title: t('_deal_volume'), key: 'volume', bigUnit: true },
    { title: 'MA5', key: 'ma5', color: colors[0] },
    { title: 'MA10', key: 'ma10', color: colors[1] },
    { title: 'MA30', key: 'ma30', color: colors[2] },
    { title: 'MA60', key: 'ma60', color: colors[3] },
])

// 图表类型
const [ baseCharts, minuteCharts ] = _.chunk(_.values(CHART_TYPES), 6),
    chartActiveTab = ref(baseCharts[0]),
    currentChartType = computed(() => {
        const [ chartType, period ] = chartActiveTab.value.split('_'),
            isKline = chartType === 'kline'

        return {
            isKline,
            period,
            axisInfoConfig: unref(isKline ? klineAxisInfoConfig : realtimeAxisInfoConfig),
        }
    })


// echart 渲染配置项
const option = ref({}),
    precision = ref(3)

// 图标固定配置
const ECHART_OPTION = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            lineStyle: {
                type: 'solid',
            },
            crossStyle: {
                type: 'solid',
            },
        },
    },
    axisPointer: {
        link: {
            xAxisIndex: 'all',
        },
        label: {
            formatter: ({ axisDimension, axisIndex, value }) => {
                if (axisDimension === 'y' && axisIndex === 1) {
                    return utils_big_amount_unit(value)
                }
                return _.isNumber(value) ? utils_currency(value, { precision: precision.value }) : value
            },
        },
    },
    grid: [
        {
            id: 'price',
            left: 0,
            right: 0,
            top: 20,
            height: 180,
        },
        {
            left: 0,
            right: 0,
            top: 180,
            height: 100,
        },
    ],
    volumeYAxis: {
        scale: true,
        gridIndex: 1,
        position: 'right',
        splitLine: false,
        splitNumber: 2,
        axisLabel: {
            inside: true,
            showMinLabel: false,
            showMaxLabel: false,
            verticalAlignMaxLabel: 'top',
            formatter: (val) => utils_big_amount_unit(val),
        },
    },
}


// 计算MA
const calculateMA = (data, period) => {
    return _.range(data.length).map((i) => {
        if (i < period - 1) {
            return null // 前几天数据不足时返回 null
        }

        // 获取当前窗口的数据并计算平均值
        const window = data.slice(i - period + 1, i + 1)
        return _.mean(window)
    })
}

// 设置分时图表配置
const onSetRealtime = () => {
    const { detail, list } = chartData.value
    // { close } = detail
    const close = detail?.close

    const dates = [],
        prices = [],
        volumes = [],
        avprice = [],
        is5Day = chartActiveTab.value === CHART_TYPES.FIVE_DAYS

    list?.forEach(e => {
        const { avgPrice, price, time, volume } = e

        if (is5Day) {
            dates.push(utils_time(time * 1000, TIME_FORMAT.YMD))
        }

        prices.push(price)
        volumes.push(volume)
        avprice.push(avgPrice)
    })

    const times = is5Day ? dates : MINUTES_RANGE

    const onSetCurrentAxisInfo = (latest, close) => {
        const lastVolume = latest?.volume ?? 0,
            lastPrice = latest?.price ?? 0,
            change = lastPrice - close

        const latestIndex = list?.length - 1

        // Calculate MA values using the existing calculateMA function
        const ma5 = calculateMA(prices, 5).at(latestIndex)
        const ma10 = calculateMA(prices, 10).at(latestIndex)
        const ma30 = calculateMA(prices, 30).at(latestIndex)
        const ma60 = calculateMA(prices, 60).at(latestIndex)

        currentAxisInfo.value = {
            ...currentAxisInfo.value,
            price: lastPrice,
            change,
            percent: change / close * 100,
            deal: lastVolume * lastPrice,
            volume: lastVolume,
            avg: latest?.avgPrice ?? 0,
            color: utils_amount_color(change),
            ma5,
            ma10,
            ma30,
            ma60,
        }
    }

    onSetCurrentAxisInfo(_.last(list), close)

    option.value = {
        tooltip: {
            ...ECHART_OPTION.tooltip,
            formatter: params => {
                const index = params[0].dataIndex

                // Calculate MA values for the current index using calculateMA function
                const ma5 = calculateMA(prices, 5).at(index)
                const ma10 = calculateMA(prices, 10).at(index)
                const ma30 = calculateMA(prices, 30).at(index)
                const ma60 = calculateMA(prices, 60).at(index)

                currentAxisInfo.value = {
                    ...currentAxisInfo.value,
                    ma5,
                    ma10,
                    ma30,
                    ma60,
                }

                onSetCurrentAxisInfo(list[index], close)
            },
        },
        axisPointer: ECHART_OPTION.axisPointer,
        grid: ECHART_OPTION.grid,
        xAxis: [
            {
                data: times,
                boundaryGap: false,
                axisLine: false,
                axisTick: false,
                axisLabel: false,
            },
            {
                data: times,
                boundaryGap: false,
                gridIndex: 1,
                axisTick: false,
                axisLine: {
                    show: false,
                },
                axisLabel: {
                    interval: is5Day ? undefined : (index) => !index || index === MINUTES_RANGE.length - 1 || index === 120,
                    alignMinLabel: 'left',
                    alignMaxLabel: 'right',
                    verticalAlign: 'middle',
                },
            },
        ],
        yAxis: [
            {
                scale: true,
                position: 'right',
                splitLine: false,
                splitNumber: 5,
                axisLabel: {
                    inside: true,
                    showMinLabel: false,
                    verticalAlignMaxLabel: 'top',
                },
            },
            ECHART_OPTION.volumeYAxis,
        ],
        series: [
            {
                name: 'Price',
                type: 'line',
                data: prices,
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: primaryColor,
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(39, 122, 248, .5)' }, // 顶部颜色
                        { offset: 1, color: 'rgba(39, 122, 248, 0)' }, // 底部颜色
                    ]),
                },
            },
            {
                name: 'Average',
                type: 'line',
                data: avprice,
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: colors[0],
                },
            },
            {
                name: 'Volume',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                data: volumes,
                itemStyle: {
                    color: ({ dataIndex }) => {
                        let chg

                        if (dataIndex) {
                            chg = prices[dataIndex] - prices[dataIndex - 1]
                        } else {
                            chg = prices[dataIndex] - close
                        }

                        if (chg > 0) {
                            return raiseColor
                        } else if (chg < 0) {
                            return fallColor
                        } else {
                            return flatColor
                        }
                    },
                },
            },
        ],
    }
}


// 设置K线图表配置
const onSetKLine = () => {

    const { detail, list } = chartData.value
    const close = detail?.close
    const latestPrice = detail?.latestPrice
    // { close, latestPrice } = detail

    const dates = [],
        prices = [],
        candlestick = [],
        volumes = []

    if (!list) return null

    list?.forEach(e => {
        const { open, close, high, low, time, volume, price } = e

        switch (chartActiveTab.value) {
            case CHART_TYPES.DAILY:
                dates.push(utils_time(time * 1000, TIME_FORMAT.MD))
                break
            case CHART_TYPES.WEEKLY:
                dates.push(utils_time(time * 1000, TIME_FORMAT.YM))
                break
            case CHART_TYPES.MONTHLY:
            case CHART_TYPES.YEARLY:
                dates.push(utils_time(time * 1000, TIME_FORMAT.Y))
                break
        }

        prices.push(price)
        candlestick.push([
            open,
            close,
            low,
            high,
        ])
        volumes.push(volume)
    })

    const times = chartActiveTab.value.includes('min') ? MINUTES_RANGE : dates

    const onSetCurrentAxisInfo = (latest, lastPrice) => {
        const change = latest?.price - lastPrice
        const latestIndex = list?.length - 1

        // Calculate MA values for K-line chart using calculateMA function
        const ma5 = calculateMA(prices, 5).at(latestIndex)
        const ma10 = calculateMA(prices, 10).at(latestIndex)
        const ma30 = calculateMA(prices, 30).at(latestIndex)
        const ma60 = calculateMA(prices, 60).at(latestIndex)

        currentAxisInfo.value = {
            ...currentAxisInfo.value,
            ...latest,
            deal: latest?.volume * latest?.price,
            volume: latest?.volume,
            color: utils_amount_color(change),
            ma5,
            ma10,
            ma30,
            ma60,
        }
    }

    onSetCurrentAxisInfo(_.last(list), list?.length > 1 ? list?.at(-2).price : close)

    const markLineColor = utils_amount_color($futures.value.gain, { raise: raiseColor, fall: fallColor, flat: flatColor })

    option.value = {
        dataZoom: [
            {
                type: 'inside',
                xAxisIndex: 'all',
                zoomOnMouseWheel: false,
                start: list?.length > 200 ? 80 : 0,
            },
        ],
        tooltip: {
            ...ECHART_OPTION.tooltip,
            formatter: params => {
                const index = params[0].dataIndex

                if (list[index]) {
                    const maValues = {
                        ma5: calculateMA(prices, 5).at(index),
                        ma10: calculateMA(prices, 10).at(index),
                        ma30: calculateMA(prices, 30).at(index),
                        ma60: calculateMA(prices, 60).at(index),
                    }

                    currentAxisInfo.value = {
                        ...currentAxisInfo.value,
                        ...maValues,
                    }

                    onSetCurrentAxisInfo(list[index], index ? list[index - 1].price : close)
                }

            },
        },
        axisPointer: ECHART_OPTION.axisPointer,
        grid: ECHART_OPTION.grid,
        xAxis: [
            {
                data: times,
                boundaryGap: false,
                axisLine: false,
                axisTick: false,
                axisLabel: false,
            },
            {
                data: times,
                boundaryGap: false,
                gridIndex: 1,
                axisTick: false,
                axisLine: {
                    show: false,
                },
                axisLabel: {
                    alignMinLabel: 'left',
                    alignMaxLabel: 'right',
                    verticalAlign: 'middle',
                    showMinLabel: true,
                    showMaxLabel: true,
                },
            },
        ],
        yAxis: [
            {
                scale: true,
                position: 'right',
                splitLine: false,
                splitNumber: 5,
                axisLabel: {
                    inside: true,
                    showMinLabel: false,
                    verticalAlignMaxLabel: 'top',
                },
            },
            ECHART_OPTION.volumeYAxis,
        ],
        series: [
            {
                name: 'Price',
                type: 'candlestick',
                data: candlestick,
                markLine: {
                    symbol: 'none',
                    lineStyle: {
                        color: markLineColor,
                        type: 'dashed',
                    },
                    label: {
                        position: 'insideEnd',
                        backgroundColor: markLineColor,
                        color: '#fff',
                        borderRadius: 4,
                        padding: [ 2, 4 ],
                    },
                    data: [
                        {
                            name: '最新价',
                            yAxis: latestPrice,
                        },
                    ],
                },
            },
            {
                name: 'MA5',
                type: 'line',
                data: calculateMA(prices, 5),
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: colors[0],
                },
            },
            {
                name: 'MA10',
                type: 'line',
                data: calculateMA(prices, 10),
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: colors[1],
                },
            },
            {
                name: 'MA30',
                type: 'line',
                data: calculateMA(prices, 30),
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: colors[2],
                },
            },
            {
                name: 'MA60',
                type: 'line',
                data: calculateMA(prices, 60),
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 1,
                    color: colors[5],
                },
            },
            {
                name: 'Volume',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                data: volumes,
                itemStyle: {
                    color: ({ dataIndex }) => {
                        let chg

                        if (dataIndex) {
                            chg = prices[dataIndex] - prices[dataIndex - 1]
                        } else {
                            chg = prices[dataIndex] - close
                        }

                        if (chg > 0) {
                            return raiseColor
                        } else if (chg < 0) {
                            return fallColor
                        } else {
                            return flatColor
                        }
                    },
                },
            },
        ],
    }
}

const chartInitial = ref(false),
    chartData = ref({
        detail: {
            market: '',
            latestPrice: 0,
            symbol: '',
            high: 0,
            low: 0,
            securityType: '',
            precision: 0,
            name: '',
            close: 0,
            open: 0,
        },
        list: [],
    }),
    lastTime = ref(0)

// 获取kline / timeline数据
const [ onGetChartData, chartLoading ] = useFetchLoading(async (minuteType) => {
    let isKline,
        period

    if (minuteType) {
        $minuteType.value = minuteType
    }

    if (minuteType) {
        const [ chartType, _period ] = minuteType.split('_')
        isKline = chartType === 'kline'
        period = _period
    } else {
        ({ isKline, period } = currentChartType.value)
    }

    const url = isKline
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/klineUsingGET_1
        ? '/futures/market/kline'
        : '/futures/market/timeLine'

    const res = await api_get({
        url,
        params: {
            period,
            instrument: $futuresInstrument.value,
        },
        options: {
            headers: {
                cancellable: false,
            },
        },
    })
    chartData.value = res

    const last = _.last(res.list)
    if (last) lastTime.value = last.time

    if (!chartInitial.value) chartInitial.value = true

    precision.value = res.detail?.precision

    if (isKline) {
        onSetKLine()
    } else {
        onSetRealtime()
    }
}, { toast: false })

// 分钟K线图配置项
const minuteOptions = computed(() => _.map(
        minuteCharts,
        e => {
            return {
                text: t(e),
                value: e,
                disabled: chartLoading.value,
            }
        },
    )),
    minuteType = ref(minuteCharts[0])

onGetChartData()

// 交易行情数据
const {
    list: tradeQuotes,
    refreshLoading,
    loadLoading,
    finished,
    onRefresh,
    onLoadMore,
} = usePagination({
    url: '/futures/market/tick/list',
    params: {
        instrument: $futuresInstrument.value,
    },
})

// 买卖队列
const { res: queue } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getBrokerDataUsingGET_1
    url: '/market/broker/queue',
    params: {
        instrument: $futuresInstrument.value,
    },
    initialValues: {
        market: '',
        symbol: '',
        securityType: '',
        buy: [],
        sell: [],
    },
})

// Q:行情数据 O:盘口数据 T:逐笔成交 B:港股经济盘
const actions = [ 'Q', 'O', 'A', 'T' ]
const symbols = actions.map(e => [ market, securityType, e, symbol ].join('|')).join(',')

socket.emit({ action: SOCKET_ACTIONS.SUBSCRIBE, symbols })
socket.on('Q', ({ data }) => {
    const [ market, securityType, latestTime, symbol, latestPrice, high, open, low, close, volume, amount ] = data.split('|'),
        _instrument = [ market, securityType, symbol ].join('|')

    const chg = +latestPrice - +close

    if (_instrument === $futuresInstrument.value) {
        $futures.value = {
            ...$futures.value,
            chg,
            latestPrice: +latestPrice,
            gain: chg / +close,
            high: +high,
            open: +open,
            amount: +amount,
            low: +low,
            volume: +volume,
        }

        // 最新时间与上次时间差
        const diff = latestTime - lastTime.value

        const isFetch = () => {
            // K线图
            if (currentChartType.value.isKline) {
                switch (chartActiveTab) {
                    case CHART_TYPES.ONE_MINUTE:
                        // 分时图直接更新
                        if (diff >= 0) return true
                        break
                    case CHART_TYPES.FIVE_MINUTES:
                        // 4分钟
                        if (diff >= 240) return true
                        break
                    case CHART_TYPES.FIFTEEN_MINUTES:
                        // 14分钟

                        if (diff >= 840) return true
                        break
                    case CHART_TYPES.THIRTY_MINUTES:
                        // 29分钟
                        if (diff >= 1740) return true
                        break
                }
            } else {
                // 非K线图且时间超过一分钟请求最新图标数据
                if (diff >= 0) return true
            }
        }

        // if (isFetch()) onGetChartData()
    }
})

// 交易体量数据格式化
const volumeFormat = (list, index) => {
    const [ price, no, vol ] = list

    return {
        depthNo: index + 1,
        no: vol ? no : '',
        price,
        vol: vol || no,
    }
}

// 盘口数据 - 买卖交易体量
socket.on('O', ({ data }) => {
    const arr = data.split('|').slice(4),
        len = market.value === 'US' ? 2 : 3,
        formatData = _.chunk(arr, len)

    const [ ask, bid ] = _.chunk(formatData, formatData.length / 2)

    $volume.value.ask = ask.map(volumeFormat)
    $volume.value.bid = bid.map(volumeFormat)
})

// 逐笔交易
socket.on('T', ({ data }) => {
    const [ market, securityType, tradeTime, symbol, tradePrice, tradeVolume, direction ] = data.split('|')
    tradeQuotes.value = [
        {
            tradePrice,
            tradeTime,
            tradeVolume,
            direction,
        },
        ...tradeQuotes.value,
    ]
})

const handleTabClick = (key) => {
    console.log(key)
    // 当正在加载时，阻止切换并直接 return
    if (chartInitial.value || chartLoading.value) return
    chartActiveTab.value = key
    onGetChartData(key)
}

watch(() => currentChartType.value.isKline, (isKline, _, onCleanup) => {
    clearInterval(timer)
    // 清除之前的定时器
    timer = setInterval(() => {
        onGetChartData($minuteType.value)
    }, 2500)

    onCleanup(() => {
        clearInterval(timer)
    })
}, { immediate: true })

onBeforeUnmount(() => {
    clearInterval(timer)
    socket.emit({ action: SOCKET_ACTIONS.DELSUBSCRIBE, symbols })
})

defineOptions({ name: 'StockQuotesTab' })
</script>

<style scoped>
.chartInfo {
    user-select: none;
    pointer-events: none;
    position: absolute;
    left: 4px;
    font-size: 10px;
}

:deep(.van-dropdown-menu__bar) {
    box-shadow: none;
    --van-dropdown-menu-background: none;
}

.bottom-shadow {
    box-shadow: 0 0 20px 4px rgba(0, 0, 0, .2);
}
</style>
