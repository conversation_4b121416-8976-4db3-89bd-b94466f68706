<template>
    <c-record
        class="bg-bg px-4"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <NewsItem
            class="not-last:border-b border-border"
            v-for="({ id, article_title, article_date, article_auth }) in list"
            :key="id"
            :title="article_title"
            :author="article_auth"
            :time="article_date"
            @click="$router.push(`/stock/news/${id}`)"
        />
    </c-record>
</template>

<script setup>
import NewsItem from '@/pages/main/news/components/NewsItem.vue'

const { $currentStock } = storeToRefs(useStockStore())

const { list, refreshLoading, loadLoading, finished, onRefresh, onLoadMore } = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCompanyInfoUsingGET_1
    url: '/market/company/news',
    params: {
        symbol: $currentStock.value.symbol,
        market: $currentStock.value.market,
        securityType: $currentStock.value.securityType,
    },
})

defineOptions({ name: 'StockNewsTab' })
</script>

<style scoped>

</style>
