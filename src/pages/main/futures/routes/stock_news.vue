<template>
    <c-header title="新闻详情"/>

    <div class="with-header-container">
        <van-skeleton
            :loading="!initial"
            title
            :row="20"
        >
            <div class="text-title text-xl">
                {{ res.article_title }}
            </div>

            <div class="flex-between gap-2.5 text-text text-xs py-3 mb-2.5 border-b border-border">
                <span>{{ res.article_auth }}</span>
                <span>{{ res.article_date }}</span>
            </div>

            <div class="text-sm text-paragraph" v-html="res.article_content"/>
        </van-skeleton>
    </div>
</template>

<script setup>
const { params: { id } } = useRoute()

const { res, initial } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getNewsInfoUsingGET_1
    url: '/market/company/news/info',
    params: {
        id,
    },
    initialValues: {
        article_title: '',
        article_market: '',
        article_symbol: '',
        article_auth: '',
        article_content: '',
        id: '',
        article_date: '',
    },
})

defineOptions({ name: 'stock-news' })
</script>

<style scoped>

</style>
