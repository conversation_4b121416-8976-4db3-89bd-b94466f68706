<template>
    <Transaction class="p-2.5">
        <!-- 股票基础信息 -->
        <div class="flex-between">
            <div>
                <p class="mb-2.5">
                    <span class="text-primary font-sm mr-2.5 ">{{ $currentFutures?.name }}</span>
                    <span class="font-sm text-primary bg-tag text-xs px-2.5 py-1">{{ $currentFutures?.market }}</span>
                </p>

                <FuturesPrice/>
            </div>

        </div>
        <!-- 股票基础信息 -->

        <!-- 股票交易量 -->
        <TransferVolume class="my-2.5"/>
        <!-- 股票交易量 -->
    </Transaction>
</template>

<script setup>
import TransferVolume from '../../components/TransferVolume.vue'
import Transaction from './components/Transaction.vue'
import FuturesPrice from '../../components/futuresPrice.vue'

const { $currentFutures } = storeToRefs(useFuturesStore())

defineOptions({ name: 'stock-transaction' })
</script>
