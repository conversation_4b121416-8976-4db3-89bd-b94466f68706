export const useWarningI18n = () => {
    return useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _title: '股票预警',
                _add: '添加预警',
                _mine: '我的预警',
                _price_raise: '股价上涨至',
                _price_fall: '股价下跌至',
                _percent_raise: '日涨幅超过',
                _percent_fall: '日跌幅超过',
                _percent: '百分比',
            },
            [LANGUAGE.zhHK]: {
                _title: '股票预警',
                _add: '添加预警',
                _mine: '我的预警',
                _price_raise: '股价上涨至',
                _price_fall: '股价下跌至',
                _percent_raise: '日涨幅超过',
                _percent_fall: '日跌幅超过',
                _percent: '百分比',
            },
            [LANGUAGE.enUS]: {
                _title: 'Stock warning',
                _add: 'Add warning',
                _mine: 'Mine warning',
                _price_raise: 'Price rose to',
                _price_fall: 'Price fell to',
                _percent_raise: 'Daily increase exceeds',
                _percent_fall: 'Daily decline exceeds',
                _percent: 'percent',
            },
        },
    })
}
