<template>
    <c-header :title="t('_add')">
        <template #right>
            <router-link class="text-xs" to="/stock/warning">
                {{ t('_mine') }}
            </router-link>
        </template>
    </c-header>

    <div class="with-header-container">
        <c-card>
            <StockBaseInfo
                inline
                v-bind="stockBaseInfo"
            />

            <c-description-group
                class="flex"
                :columns="isEn ? 2 : 3"
            >
                <c-description
                    v-for="({ label, value, colorful, percent, symbol }) in details"
                    :key="label"
                    :label
                >
                    <c-amount
                        :amount="value"
                        :colorful
                        :percent
                        :symbol
                    />
                </c-description>
            </c-description-group>
        </c-card>

        <c-card class="mt-4" no-padding>
            <van-form
                :label-width="isEn ? '160px' : undefined"
            >
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="50"
                    type="number"
                    inputmode="decimal"
                    name="targetUpPrice"
                    clearable
                    :label="t('_price_raise')"
                    :placeholder="t('form.input_placeholder', [ t('stock.transaction.price') ])"
                    v-model="formState.targetUpPrice"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="100"
                    type="number"
                    inputmode="decimal"
                    name="targetDownPrice"
                    clearable
                    :label="t('_price_fall')"
                    :placeholder="t('form.input_placeholder', [ t('stock.transaction.price') ])"
                    v-model="formState.targetDownPrice"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="150"
                    type="number"
                    inputmode="decimal"
                    name="targetUpGain"
                    clearable
                    :label="t('_percent_raise')"
                    :placeholder="t('form.input_placeholder', [ t('_percent') ])"
                    v-model="formState.targetUpGain"
                >
                    <template #right-icon>
                        <span class="text-raise">%</span>
                    </template>
                </van-field>
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="200"
                    type="number"
                    inputmode="decimal"
                    name="targetDownGain"
                    clearable
                    :label="t('_percent_fall')"
                    :placeholder="t('form.input_placeholder', [ t('_percent') ])"
                    v-model="formState.targetDownGain"
                >
                    <template #right-icon>
                        <span class="text-fall">%</span>
                    </template>
                </van-field>
            </van-form>
        </c-card>

        <c-submit
            data-aos-delay="250"
            class="mt-10"
            :loading
            :disabled
            @click="onSubmit"
        >
            {{ t('operation.save') }}
        </c-submit>
    </div>
</template>

<script setup>
import _ from 'lodash'

import { useWarningI18n } from './hooks.js'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'

const { query } = useRoute(),
    { back } = useRouter()

const { t, locale } = useWarningI18n(),
    isEn = locale.value === LANGUAGE.enUS

const stockInfo = ref({
    ...stockInfoInitial,
})

const { run } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getSymbolWarnBySymbolUsingGET_1
    url: '/symbol/warn/getBySymbol',
    needLogin: true,
    manual: true,
    onSuccess: (res) => {
        formState.value = {
            ...res,
        }
    },
})

const _onGetStockInfo = async () => {
    stockInfo.value = await onGetStockInfo({
        instrument: _.values(query).join('|'),
    })
    run(query)
}
_onGetStockInfo()

const stockBaseInfo = computed(() => {
    const { market, securityType, symbol, name } = stockInfo.value

    return { market, securityType, symbol, name }
})

const details = computed(() => {
    const { latestPrice, chg, gain } = stockInfo.value

    return [
        {
            label: t('stock.current_price'),
            value: latestPrice,
        },
        {
            label: t('stock.change'),
            value: chg,
            colorful: true,
            symbol: true,
        },
        {
            label: t('stock.change_percent'),
            value: gain,
            colorful: true,
            symbol: true,
            percent: true,
        },
    ]
})

const formState = ref({
    targetUpPrice: '',
    targetDownPrice: '',
    targetUpGain: '',
    targetDownGain: '',
})

const disabled = useFormDisabled(formState, [], false)

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { targetUpPrice, targetDownPrice, targetUpGain, targetDownGain } = formState.value

    let url, method

    if (formState.value?.id) {
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/updateSymbolWarnUsingPUT_1
        url = '/symbol/warn/update'
        method = FETCH_METHOD.PUT
    } else {
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/addSymbolWarnUsingPOST_1
        url = '/symbol/warn/add'
        method = FETCH_METHOD.POST
    }

    await api_fetch({
        url,
        method,
        params: {
            ...stockBaseInfo.value,
            targetUpPrice: +targetUpPrice,
            targetDownPrice: +targetDownPrice,
            targetUpGain: +targetUpGain,
            targetDownGain: +targetDownGain,
            id: formState.value?.id,
        },
    })

    showSuccessToast(t('operation.successfully'))

    back()
})

defineOptions({ name: 'stock-warning-add' })
</script>

<style scoped>

</style>
