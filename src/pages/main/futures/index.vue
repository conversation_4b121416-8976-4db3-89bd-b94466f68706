<!-- 交易/行情tab -->
<template>
    <c-header>
        <!-- Use SkewTabs when isTempCoin is true, otherwise use van-tabs -->
        <SkewTabs
            v-if="isTempCoin"
            class="w-max mx-auto"
            v-model="$futuresActiveTab"
            width="60%"
            height="30px"
            :tabs="skewTabsData"
            :is-temp-coin="true"
            @change="handleSkewTabChange"
        />
        
        <van-tabs
            v-else
            class="w-max mx-auto"
            type="card"
            v-model:active="$futuresActiveTab"
            @click-tab="onClickTab"
        >
            <van-tab
                :title="$t('stock.transition')"
                name="futures-transaction"
            />
            <van-tab
                :title="$t('stock.quotes')"
                name="futures-details"
            />
        </van-tabs>
    </c-header>

    <div class="with-header-container__noPadding">
        <router-view/>
    </div>
</template>

<script setup>
import { useFuturesStore } from '@/store/futures.js'
import SkewTabs from '@/components/SkewTabs/index.vue'
import { useI18n } from 'vue-i18n'  

const route = useRoute()
const { onRedirect } = useStockRedirect()
const { t } = useI18n()

// Check if current template is temp_coin
const isTempCoin = computed(() => {
    return G_TEMPLATE === 'temp_coin'
})

// SkewTabs data format
const skewTabsData = computed(() => {
    return [
        {
            label: t('stock.transition'),
            value: 'futures-transaction',
            key: 'futures-transaction',
        },
        {
            label: t('stock.quotes'),
            value: 'futures-details',
            key: 'futures-details',
        },
    ]
})

// Handle SkewTabs change
const handleSkewTabChange = (tab) => {
    onRedirect(tab.value)
    $futuresActiveTab.value = tab.value
}

const onClickTab = ({ name }) => {
    onRedirect(name)
    $futuresActiveTab.value = name
}

const futuresStore = useFuturesStore()
const { $futuresActiveTab } = storeToRefs(futuresStore)
const { dispatch_futures_activeTab } = futuresStore

watch(() => route.fullPath, (nv, ov) => {
    if (nv !== ov) {
        if (nv === '/futures/transaction') {
            dispatch_futures_activeTab('futures-transaction')
        } else if (nv === '/futures/details') {
            dispatch_futures_activeTab('futures-details')
        }
    }
})

// 期货详情
defineOptions({ name: 'futures' })
</script>

<style scoped>
:deep(.c-header__left) {
    width: 42px;
}
</style>
