<template>
    <div :class="[ inline ? 'h-5 flex-middle gap-1' : 'w-full' ]">
        <div class="text-title text-xs h-5 leading-5 font-semibold truncate" :class="{ 'w-full': !inline }">
            {{ name }}
        </div>

        <div class="flex-middle gap-1">
            <div class="marketBlock" v-if="market">{{ market }}</div>
            <div class="text-[9px]">{{ symbol }}</div>
        </div>
    </div>
</template>

<script setup>
// import { stockMarketDict } from '@/config/index.js'

defineProps({
    name: String,
    market: {
        type: String,
        required: true,
    },
    symbol: {
        type: String,
        required: true,
    },
    inline: Boolean,
})

defineOptions({ name: 'StockBaseInfo' })
</script>

<style scoped>

</style>
