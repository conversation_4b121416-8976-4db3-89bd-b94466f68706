<template>
    <c-description-group
        class="my-2.5"
        :items
    />
</template>

<script setup>
import currencyJs from 'currency.js'
import Amount from '@/components/Amount/index.vue'

const { direction, available, dataSource, label, currency, prefix, totalRate, isIndex, quantity } = defineProps({
    // 数据
    dataSource: {
        type: Object,
        required: true,
    },
    // 交易方向
    direction: {
        type: Number,
        required: true,
    },
    // 可用数量
    available: {
        type: Number,
        required: true,
    },
    // 可用数量标题
    label: {
        type: String,
        required: true,
    },
    // 币种
    currency: {
        type: String,
        required: true,
    },
    prefix: Array,
    // 是否对总价进行汇率换算
    totalRate: Boolean,
    // 是否股指
    isIndex: Boolean,
    quantity: [ Number, String ],
})

const { dispatch_getCurrencyRateConfig } = useRateStore()

const items = computed(() => {
    const { rate } = dispatch_getCurrencyRateConfig(currency)

    const { buyTotal, buyFee, sellTotal, sellFee } = dataSource

    let total, fee
    if (direction === 1) {
        total = buyTotal
        fee = buyFee
    } else {
        total = sellTotal
        fee = sellFee
    }

    let base = [
        {
            label,
            value: () => h(Amount, {
                amount: available,
                precision: isIndex ? 1 : 0,
                suffix: isIndex ? t('_board') : t('stock.lot'),
            }),
        },
        // 手续费
        {
            label: t('stock.transaction.fee'),
            value: () => h(Amount, {
                amount: Number(quantity) ? fee : '0.00',
                currency,
            }),
        },
        // 订单金额
        {
            label: t('_order'),
            value: () => h(Amount, {
                amount: dataSource.order,
                currency,
            }),
        },
        // 总价
        {
            label: t('stock.transaction.total_price'),
            tip: t('_total_tip'),
            value: () => h(Amount, {
                amount: Number(quantity) ? total : '0.00',
                currency,
            }),
        },
    ]

    if (totalRate) {
        base = [
            ...base,
            {
                label: '',
                value: () => h(Amount, {
                    prefix: '≈',
                    amount: Number(quantity) ? currencyJs(total).divide(rate) : '0.00',
                    currency: CURRENCY.CNY,
                }),
            },
        ]
    }

    return prefix ? [ ...prefix, ...base ] : base
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _board: '手',
            _order: '订单金额',
            _total_tip: '股票市场数据实时波动，提交后可能与当前价格存在差异，请以最终成交价格为准。',
        },
        [LANGUAGE.zhHK]: {
            _board: '手',
            _order: '订单金额',
            _total_tip: '股票市场数据实时波动，提交后可能与当前价格存在差异，请以最终成交价格为准。',
        },
        [LANGUAGE.enUS]: {
            _board: 'Board Lot',
            _order: 'Order Amount',
            _total_tip: 'Stock market data fluctuates in real time and may differ from the current price after submission. Please refer to the final transaction price.',
        },
    },
})

defineOptions({ name: 'StockTransactionPriceDetails', inheritAttrs: false })
</script>

<style scoped>

</style>
