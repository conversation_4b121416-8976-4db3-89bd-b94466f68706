<template>
    <Transaction class="p-2.5">
        <!-- 股票基础信息 -->
        <div class="flex-between">
            <div>
                <div class="text-title">
                    {{ $stock.name }} {{ $stock.symbol }}
                </div>

                <StockPrice/>
            </div>

            <div class="text-link text-sm" @click="handleDetailClick">
                {{ $t('common.details') }}
            </div>
        </div>
        <!-- 股票基础信息 -->

        <!-- 股票交易量 -->
        <TransferVolume v-if="+type === 1" class="my-2.5"/>
        <!-- 股票交易量 -->
    </Transaction>
</template>

<script setup>
import TransferVolume from '../../components/TransferVolume.vue'
import Transaction from './components/Transaction.vue'
import StockPrice from '../../components/StockPrice.vue'
import { STOCK_ROUTE } from '@/config/index.js'

const { $stock, $stockActiveTab } = storeToRefs(useStockStore())
const router = useRouter()
const { params: { type } } = useRoute()

const handleDetailClick = (id) => {
    router.replace({ name: 'stock-details' })
    $stockActiveTab.value = STOCK_ROUTE.DETAILS_NAME
}

defineOptions({ name: 'stock-transaction' })
</script>
