<template>
    <c-header :title="$plateParams.title"/>

    <div class="with-header-container__noPadding">
        <c-table
            class="h-full"
            :finished
            :data-source="list"
            :columns
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            @row-click="dispatch_checkStock"
        />
    </div>
</template>

<script setup>
const { dispatch_checkStock } = useStockStore(),
    { $plateParams } = storeToRefs(useQuotesStore())

const columns = useStockColumns()

const { list, refreshLoading, loadLoading, finished, onRefresh, onFilter, onLoadMore } = usePagination({
    url: '/market/plateInfo',
    params: $plateParams.value,
}, {
    responseKeys: {
        list: 'list',
        total: 'totalNum',
    },
})

// 根据板块类型查看全部股票
defineOptions({ name: 'stock-industry-type' })
</script>

<style scoped>

</style>
