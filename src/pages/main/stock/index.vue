<template>
    <c-header>
        <!-- Use SkewTabs when isTempCoin is true, otherwise use van-tabs -->
        <SkewTabs
            v-if="isTempCoin"
            class="w-max mx-auto"
            v-model="$stockActiveTab"
            :tabs="skewTabsData"
            :is-temp-coin="true"
            width="60%"
            height="30px"
            @change="handleSkewTabChange"
        />
        
        <van-tabs
            v-else
            class="w-max mx-auto"
            type="card"
            v-model:active="$stockActiveTab"
            @click-tab="onClickTab"
        >
            <van-tab
                :title="$t('stock.transition')"
                :name="STOCK_ROUTE.TRANSACTION"
            />
            <van-tab
                :title="$t('stock.quotes')"
                :name="STOCK_ROUTE.DETAILS_NAME"
            />
        </van-tabs>

        <template #right>
            <van-icon
                name="search"
                class="mr-2.5"
                @click="push('/stock/search')"
            />
        </template>
    </c-header>

    <div class="with-header-container__noPadding">
        <router-view/>
    </div>
</template>

<script setup>
import { STOCK_ROUTE } from '@/config'
import SkewTabs from '@/components/SkewTabs/index.vue'
import { useI18n } from 'vue-i18n'  

const route = useRoute()
const { onRedirect } = useStockRedirect()
const { t } = useI18n()

const accountStore = useAccountStore()
const { $tradePageContractId } = storeToRefs(accountStore)
$tradePageContractId.value = ''

// Check if current template is temp_coin
const isTempCoin = computed(() => {
    return G_TEMPLATE === 'temp_coin'
})

// SkewTabs data format
const skewTabsData = computed(() => {
    return [
        {
            label: t('stock.transition'),
            value: STOCK_ROUTE.TRANSACTION,
            key: 'transaction',
        },
        {
            label: t('stock.quotes'),
            value: STOCK_ROUTE.DETAILS_NAME,
            key: 'quotes',
        },
    ]
})

// Handle SkewTabs change
const handleSkewTabChange = (tab) => {
    onRedirect(tab.value)
    $stockActiveTab.value = tab.value
}

const onClickTab = ({ name }) => {
    onRedirect(name)
    $stockActiveTab.value = name
}

const { push } = useRouter()

const { $stockActiveTab } = storeToRefs(useStockStore())

$stockActiveTab.value = route.path.includes('transaction')
    ? 'transaction'
    : 'stock-details'

// const onRedirectQuotes = () => {
//     const marketType = stockCountryDict.get($currentStock.value.market)
//     $marketType.value = marketType
//     $plateType.value = STOCK_CONFIG[marketType].plate[0]
//
//     const redirect = +$currentStock.value.securityType === 1 ? '/quotes/stock' : '/quotes/index'
//     push(redirect)
// }

onBeforeUnmount(() => {
    $stockActiveTab.value = STOCK_ROUTE.DETAILS_NAME
})

// 个股详情
defineOptions({ name: 'stock' })
</script>

<style scoped>
:deep(.c-header__left) {
    width: 42px;
}
</style>
