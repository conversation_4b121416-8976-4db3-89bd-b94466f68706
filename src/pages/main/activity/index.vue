<template>
    <div class="with-header-container">
        <!--<van-tabs-->
        <!--    class="h-full"-->
        <!--    shrink-->
        <!--    v-model:active="$activityActiveTab"-->
        <!--&gt;-->
        <!--    <van-tab-->
        <!--        :title="t('header.news')"-->
        <!--        name="news"-->
        <!--        class="pt-2.5"-->
        <!--    >-->
        <!--        <NewsList/>-->
        <!--    </van-tab>-->

        <!--    <van-tab-->
        <!--        :title="t('header.activity')"-->
        <!--        name="activity"-->
        <!--        class="pt-2.5"-->
        <!--    >-->
        <NoticeList :type="NOTICE_TYPE.ACTIVITY"/>
        <!--    </van-tab>-->
        <!--</van-tabs>-->
    </div>
</template>

<script setup>
import { $activityActiveTab, NOTICE_TYPE } from './store.js'
import NewsList from '@/pages/main/news/components/_TEMPLATE_/NewsList.vue'
import NoticeList from '@/pages/main/activity/components/_TEMPLATE_/NoticeList.vue'

import { $theme } from '@/store/index.js'
import bgImageLight from '/skins/templates/_TEMPLATE_/_THEME_/bg_light2.png'
import bgImageDark from '/skins/templates/_TEMPLATE_/_THEME_/bg_dark2.png'

const { t } = useI18n()

onMounted(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        appEl.style.background = `var(--page) url(${$theme.value === 'light' ? bgImageLight : bgImageDark}) no-repeat top center`
        appEl.style.backgroundSize = 'contain, auto'
    }
})

onBeforeUnmount(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        if (appEl) {
            appEl.style.background = ''
            appEl.style.backgroundSize = ''
            appEl.style.backgroundPositionY = ''
        }
    }
})

defineOptions({ name: 'activity' })
</script>

<style scoped>
</style>
