<template>
    <van-pull-refresh
        v-model="$noticeRefreshLoading"
        @refresh="dispatch_refreshNotice"
    >
        <van-skeleton :loading="!$noticeInitial">
            <template #template>
                <div class="w-full">
                    <c-card
                        class="mb-2.5"
                        v-for="n in 4"
                        :key="n"
                    >
                        <van-skeleton-image class="w-full! mb-2.5"/>

                        <van-skeleton-paragraph/>
                    </c-card>
                </div>
            </template>

            <div
                data-aos="fade-left"
                data-aos-anchor="#app"
                class="bg-bg rounded overflow-hidden not-last:mb-2.5"
                v-for="(item, i) in $notice[type]"
                :key="item.id"
                :data-aos-delay="50 * i"
                @click="$activityDetails = item; $router.push('/activity/details')"
            >
                <img
                    class="w-full h-[126px] object-center"
                    :src="item.imageUrl"
                    :alt="item.title"
                >

                <div class="leading-10 text-link font-semibold px-2.5">{{ item.title }}</div>
            </div>
        </van-skeleton>
    </van-pull-refresh>
</template>

<script setup>
import { $activityDetails, NOTICE_TYPE } from '../../store'

defineProps({
    type: {
        type: String,
        required: true,
        validator(value) {
            return Object.values(NOTICE_TYPE).includes(value)
        },
    },
})

const noticeStore = useNoticeStore(),
    { dispatch_refreshNotice } = noticeStore,
    { $notice, $noticeInitial, $noticeRefreshLoading } = storeToRefs(noticeStore)

defineOptions({ name: 'NoticeList' })
</script>

<style scoped>

</style>
