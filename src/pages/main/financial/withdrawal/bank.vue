<template>
    <form @submit="onValid">
        <c-card
            data-aos-delay="50"
            class="mb-4"
            :title="t('financial.receiver')"
        >
            <!-- 请选择银卡卡 -->
            <BankSelector
                data-aos="fade-left"
                data-aos-delay="50"
                :placeholder="t('form.select_placeholder', [ t('financial.bank_card') ])"
                v-model="formState.userBankCardId"
                v-model:index="bankIndex"
            />
            <!-- 请选择银卡卡 -->
            
            <van-field
                data-aos="fade-left"
                data-aos-delay="50"
                :label="t('financial.bank_owner')"
                readonly
                :model-value="currentBankInfo.realName"
            />
            <van-field
                data-aos="fade-left"
                data-aos-delay="100"
                :label="t('financial.bank_number')"
                :model-value="currentBankInfo.bankAccount"
                readonly
            />
        </c-card>

        <FinancialAmount
            :title="t('financial.withdrawal.amount')"
            :min="$withdrawalConfig.minWithdrawalAmount"
            :max
            v-model="formState.amount"
        />

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="150"
            :disabled
            :loading="submitLoading"
        />
    </form>

    <Password/>
</template>

<script setup>
import _ from 'lodash'

import BankSelector from '../components/BankSelector.vue'
import FinancialAmount from '../components/FinancialAmount.vue'
import { usePassword } from '@/hooks/index.js'

const { $profile } = storeToRefs(useProfileStore())

const { dispatch_refreshSpot } = useAccountStore(),
    { $bankList } = storeToRefs(useBankStore()),
    { $withdrawalConfig } = storeToRefs(useWithdrawalStore()),
    { $spot } = storeToRefs(useAccountStore())

const bankIndex = ref(-1),
    currentBankInfo = computed(() => $bankList.value[bankIndex.value] ?? {})

const max = computed(() => Math.min($withdrawalConfig.value.maxWithdrawalAmount, $spot.value.usableCash))

const formState = ref({
    userBankCardId: null,
    amount: '',
})

const disabled = useFormDisabled(formState)

const [ onSubmit, submitLoading ] = useFetchLoading(async (password) => {
    await api_post({
        url: API_PATH.WITHDRAWAL,
        params: {
            amount: formState.value.amount,
            userBankCardId: formState.value.userBankCardId,
            password: btoa(password),
        },
    })

    formState.value.amount = ''

    showSuccessToast(t('operation.successfully'))

    dispatch_refreshSpot()
})

const { onOpenPassword, Password } = usePassword(onSubmit)

const onValid = e => {

    if (!$withdrawalConfig.value.testUsersCanWithdraw && +$profile.value.type === 2) {
        showFailToast(t('operation.test_account_cannot_withdraw'))
        return false
    }

    e.preventDefault()

    const { amount } = formState.value,
        { minWithdrawalAmount } = $withdrawalConfig.value

    if (_.inRange(amount, minWithdrawalAmount, max.value + 1)) {
        onOpenPassword()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const { t } = useI18n()

defineOptions({ name: 'withdrawal-bank' })
</script>

<style scoped>
.van-cell {
    padding: 16px 12px;
}
</style>
