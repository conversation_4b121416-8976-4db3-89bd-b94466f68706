<template>
    <c-header :title="t('_title')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-4"
            v-for="({ id, channelName, orderAmount, status, createTime, orderNo }, i) in list"
            :data-aos-delay="i * 50"
            :key="id"
        >
            <c-description :label="t('_channel')" :value="channelName"/>
            <c-description :label="t('financial.deposit.amount')">
                <c-amount :amount="orderAmount"/>
            </c-description>
            <c-description :label="t('common.status')">
                <span :class="`text-${STATUS_DICT[status]?.color}`">
                    {{ t(`dict.${STATUS_DICT[status]?.label}`) }}
                </span>
            </c-description>

            <!-- 订单编号-->
            <c-description :label="t('_order')">
                <span>{{ orderNo }}</span>
            </c-description>
            <c-description :label="t('common.create_time')" :value="createTime"/>
        </c-card>
    </c-record>
</template>

<script setup>
const STATUS_DICT = {
    0: {
        label: '等待支付',
        color: '',
    },
    1: {
        label: '交易成功',
        color: 'green',
    },
    2: {
        label: '交易失败',
        color: 'red',
    },
    3: {
        label: '未知结果',
        color: 'special',
    },
}

const { list, refreshLoading, loadLoading, finished, onRefresh, onLoadMore } = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E5%85%85%E5%80%BC%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86/pageOrderUsingGET_1
    url: '/payOrder/page',
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '充值记录',
            _channel: '充值渠道',
            _order: '订单编号',
        },
        [LANGUAGE.zhHK]: {
            _title: '充值记录',
            _channel: '充值渠道',
            _order: '訂單編號',
        },
        [LANGUAGE.enUS]: {
            _title: 'Deposit Record',
            _channel: 'Deposit Channel',
            _order: 'Order Number',
        },
    },
})

defineOptions({ name: 'deposit-record' })
</script>

<style scoped>

</style>
