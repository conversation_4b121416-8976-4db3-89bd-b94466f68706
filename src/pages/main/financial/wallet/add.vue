<template>
    <c-header :title="t('financial.wallet.accountAddress')"/>

    <form class="with-header-container" @submit="onSubmit">
        <c-card>
            <c-input
                :label="t('_address')"
                :placeholder="t('form.input_placeholder', [ t('_address') ])"
                v-model="formState.payAddress"
            />

            <c-select
                class="mt-4"
                :label="t('_channel')"
                :placeholder="t('form.select_placeholder', [ t('_channel') ])"
                :columns="$allWallet"
                :columns-field-names="{
                    text: 'bankName',
                    value: 'bankCode'
                }"
                v-model="formState.bankCode"
            >
                <template #option="{icon, bankName}">
                    <div class="px-5 flex w-full items-center">
                        <img class="w-[30px] h-[30px] mr-2.5" :src="icon" alt="icon">
                        <span class="text-primary">{{ bankName }}</span>
                    </div>
                </template>
            </c-select>
        </c-card>

        <c-submit
            class="mt-10 mb-4"
            :disabled
            :loading="submitLoading"
        />

        <c-service/>
    </form>
</template>

<script setup>
const walletStore = useWalletStore(),
    { dispatch_refreshWallet } = walletStore,
    { $allWallet } = storeToRefs(walletStore)

const { back } = useRouter()

const formState = ref({
    payAddress: '',
    bankCode: null,
})

const disabled = useFormDisabled(formState)

const [ onSubmit, submitLoading ] = useFetchLoading(async (e) => {
    e.preventDefault()

    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%94%A8%E6%88%B7%E9%92%B1%E5%8C%85/bindUsingPOST_1
        url: '/user_wallet/bind',
        params: formState.value,
    })

    formState.value = {
        payAddress: '',
        bankCode: null,
    }

    showSuccessToast(t('operation.successfully'))

    Promise.all([
        dispatch_refreshWallet(),
        back(),
    ])
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _address: '账户地址',
            _channel: '账户类型',
        },
        [LANGUAGE.zhHK]: {
            _address: '帳戶地址',
            _channel: '帳户类型',
        },
        [LANGUAGE.enUS]: {
            _address: 'Account Address',
            _channel: 'Account Type',
        },
    },
})

defineOptions({ name: 'wallet-add' })
</script>