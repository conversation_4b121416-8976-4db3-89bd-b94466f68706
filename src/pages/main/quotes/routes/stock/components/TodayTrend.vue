<template>
    <div class="w-full" :key="$raise_fall_color">
        <c-echarts
            v-if="data?.length"
            style="height: 200px;"
            :option
        />

        <van-empty v-else :description="$t('common.empty')"/>
    </div>
</template>

<script setup>
import { $raise_fall_color } from '@/store/index.js'

const { data } = defineProps({
    data: {
        type: Array,
        required: true,
    },
})

const option = computed(() => {
    const [ raiseColor, fallColor, flatColor ] = useCssVariable([ 'raise', 'fall', 'text' ])

    return {
        grid: {
            left: 5,
            top: 20,
            bottom: 5,
            right: 5,
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                status: 'shadow',
                type: 'shadow',
            },
        },
        xAxis: {
            type: 'category',
            data: [ '>20', '15-20', '10-15', '5-10', '0-5', '0', '0-5', '5-10', '10-15', '15-20', '>20' ],
            // data: [ '<20', '20-15', '15-10', '10-5', '5-0', '0', '0-5', '5-10', '10-15', '15-20', '>20' ],
            axisTick: false,
            axisLine: {
                show: false,
            },
            axisLabel: {
                customValues: [ 0, 2, 4, 6, 8, 10 ],
            },
        },
        yAxis: {
            show: false,
        },
        series: [
            {
                type: 'bar',
                barCategoryGap: 4,
                itemStyle: {
                    borderRadius: [ 4, 4, 0, 0 ],
                    color({ dataIndex }) {
                        if (dataIndex === 5) return flatColor
                        if (dataIndex < 5) return fallColor
                        if (dataIndex > 5) return raiseColor
                    },
                },
                label: {
                    show: true,
                    position: 'top',
                    formatter({ dataIndex, data }) {
                        if (dataIndex === 5) {
                            return `{one|${data}}`
                        }
                        if (dataIndex < 5) {
                            return `{two|${data}}`
                        }
                        if (dataIndex > 5) {
                            return `{three|${data}}`
                        }
                    },
                    rich: {
                        one: { color: flatColor },
                        two: { color: fallColor },
                        three: { color: raiseColor },
                    },
                },
                data,
            },
        ],
    }
})

// 今日大盘走势
defineOptions({ name: 'TodayTrend' })
</script>

<style scoped>
.van-empty {
    padding: 0;
}
</style>
