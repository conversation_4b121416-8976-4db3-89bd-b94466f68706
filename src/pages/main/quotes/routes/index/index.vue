<template>
    <div class="h-full overflow-x-hidden overflow-y-auto pb-4">
        <!-- 当前股指基础信息 -->
        <div
            data-aos="fade-left"
            class="flex-middle gap-2 font-semibold text-sm text-paragraph h-10"
        >
            <StockBaseInfo
                inline
                :symbol="currentIndexDetails.symbol"
                :name="currentIndexDetails.name"
                :market="currentIndexDetails.market"
            />

            <c-amount
                symbol
                colorful
                percent
                :precision="2"
                :amount="currentIndexDetails.gain * 100"
            />

            <div
                class="text-xs ml-auto text-link"
                @click="dispatch_checkStock($stock)"
            >
                {{ t('common.details') }}
            </div>
        </div>
        <!-- 当前股指基础信息 -->

        <IndexTrend
            optional
            @select="onSelect"
        />

        <Transaction
            class="mt-2 h-full overflow-y-auto overflow-x-hidden"
            type="index"
            ref="transactionRef"
            :tradeDisabled="!config.isAllowTrade"
            :timingDisabled="!config.isTimerClose"
            v-model:mode="tradeMode"
        >
            <!-- 定时平仓 -->
            <template #indexTiming="{ position }">
                <!-- 持仓选择 -->
                <IndexPositionSelector
                    data-aos-delay="50"
                    :columns="position"
                    v-model="positionId"
                />
                <!-- 持仓选择 -->

                <!-- 定时平仓时间选择 -->
                <c-select
                    data-aos="fade-left"
                    data-aos-delay="100"
                    class="mt-2.5"
                    :placeholder="t('form.select_placeholder', [ t('_timing') ])"
                    popover
                    :columns="periodOptions"
                    v-model="timerValue"
                />
                <!-- 定时平仓时间选择 -->

                <c-submit
                    class="my-2.5"
                    data-aos-delay="150"
                    :loading
                    :disabled
                    @click="onSubmit"
                />

                <van-button
                    data-aos="fade-left"
                    data-aos-delay="150"
                    block
                    to="/index/results"
                >
                    {{ t('_results') }}
                </van-button>
            </template>
            <!-- 定时平仓 -->
        </Transaction>
    </div>
</template>

<script setup>
import _ from 'lodash'
import IndexTrend from './components/IndexTrend.vue'
import Transaction from '@/pages/main/stock/routes/transaction/components/Transaction.vue'
import IndexPositionSelector from './components/IndexPositionSelector.vue'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'

const transactionRef = useTemplateRef('transactionRef')
const { dispatch_refreshAccount } = useAccountStore(),
    { $indexData } = storeToRefs(useIndexStore()),
    stockStore = useStockStore(),
    { dispatch_checkStock } = stockStore,
    { $instrument, $stock, $currentStock } = storeToRefs(stockStore)

const currentIndexDetails = computed(() => $indexData.value[$instrument.value]?.details ?? {
    market: '',
    name: '',
    symbol: '',
    gain: 0,
})

// 交易模式 1:持仓交易 | 2:定时平仓
const tradeMode = ref(1)

// 股指切换
const onSelect = async (_instrument) => {
    const [ market, securityType, symbol ] = _instrument.split('|')
    // 请求股指基础数据
    await dispatch_checkStock({ market, securityType, symbol }, { redirect: false })

    // 获取对应股指的账户数据、手续费
    transactionRef.value?.onChangIndex()
    // 修改最新数据
    $indexData.value[_instrument].details = {
        ...$stock.value,
    }

    positionId.value = null
    tradeMode.value = 1
}

onMounted(() => {
    const indexData = _.keys($indexData.value)
    // 非股指默认进入展示股指第一个
    if (+$currentStock.value.securityType !== 2 && indexData.length) onSelect(indexData[0])
})

// 股指配置
const config = computed(() => $indexData.value[$instrument.value]?.config ?? {
    isAllowTrade: false,
    isTimerClose: false,
    timerValue: '',
})

// 股指配置的定时
const periodOptions = computed(() => config.value.timerValue.split(',').map(e => ({
    text: t('_minutes', [ e ]),
    value: e,
})))

const positionId = ref(null),
    timerValue = ref(null)

const disabled = computed(() => !positionId.value || !timerValue.value)

const [ onSubmit, loading ] = useFetchLoading(async () => {
    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E6%8C%81%E4%BB%93%E7%9B%B8%E5%85%B3/setExpireTimeUsingGET_1
        url: '/position/setExpireTime',
        params: {
            positionId: positionId.value,
            timerValue: timerValue.value,
        },
    })

    showSuccessToast(t('operation.successfully'))

    await Promise.all([
        transactionRef.value?.onSelectAccount(),
        dispatch_refreshAccount(),
    ])
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _timing: '平仓时间',
            _minutes: '{0}分钟',
            _results: '查看开奖结果',
        },
        [LANGUAGE.zhHK]: {
            _timing: '平仓时间',
            _minutes: '{0}分钟',
            _results: '查看开奖结果',
        },
        [LANGUAGE.enUS]: {
            _timing: 'Timing',
            _minutes: '{0} Mins',
            _results: 'View results',
        },
    },
})

defineOptions({ name: 'quotes-index' })
</script>

<style scoped>
</style>
