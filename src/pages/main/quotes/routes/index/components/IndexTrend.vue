<template>
    <Swiper
        data-aos="fade-left"
        data-aos-delay="50"
        class="h-[172px]"
        :key="$raise_fall_color"
        :slides-per-view="3"
        :space-between="10"
        :modules="[ Pagination ]"
        :pagination="{}"
        watch-slides-progress
    >
        <SwiperSlide
            v-slot="{ isVisible }"
            v-for="({ timeline, details: { name, latestPrice, chg, gain, market, symbol }, isShow, config: {currency} }, instrument) in $indexData"
            :key="instrument"
            @click="onSelect(instrument)"
        >
            <IndexTrendChild
                :key="instrument"
                v-bind="{optional, timeline, name, latestPrice, chg, gain, instrument, isVisible, isShow, currency, market, symbol }"
            />
        </SwiperSlide>
    </Swiper>
</template>

<script setup>
import IndexTrendChild from './IndexTrendChild.vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

import { $raise_fall_color } from '@/store/index.js'
import socket from '@/socket.js'
import { SOCKET_EVENTS } from '@/config/index.js'

defineProps({
    optional: Boolean,
})

const emits = defineEmits([ 'select' ])


const indexStore = useIndexStore(),
    { dispatch_refreshIndex } = indexStore,
    { $indexData } = storeToRefs(indexStore)

const onSelect = val => {
    emits('select', val)
}

useSocket(SOCKET_EVENTS.UPDATE_INDEX, dispatch_refreshIndex)

socket.on('Q', ({ data }) => {
    const [ market, securityType, latestTime, symbol, latestPrice, high, open, low, close, volume, amount ] = data.split('|')

    const instrument = [ market, securityType, symbol ].join('|')

    const chg = +latestPrice - +close
    if ($indexData.value[instrument]) {
        $indexData.value[instrument].details = {
            ...$indexData.value[instrument].details,
            chg,
            latestPrice: +latestPrice,
            gain: chg / +close,
            high: +high,
            open: +open,
            amount: +amount,
            low: +low,
            volume: +volume,
        }
        // $indexData.value[instrument].timeline.push({
        //     avgPrice: +latestPrice,
        //     price: +latestPrice,
        //     time: latestTime,
        //     volume,
        // })
    }
})

defineOptions({ name: 'IndexTrend' })
</script>

<style scoped>
.swiper {
    --swiper-pagination-bottom: 0;
    --swiper-theme-color: var(--primary);
}
</style>
