<template>
    <div :key="$raise_fall_color" class="text-sm rounded-lg bg-card custom-shadow relative py-2.5">
        <IndexTrendChild
            v-for="(item, idx) in Object.values($indexData).slice(0, 6)"
            :key="item.details?.symbol || idx"
            @click="onSelect(item.details?.market + '|' + item.details?.securityType + '|' + item.details?.symbol)"
            v-bind="{
                optional,
                timeline: item.timeline,
                name: item.details?.name,
                latestPrice: item.details?.latestPrice,
                chg: item.details?.chg,
                gain: (+item.details?.latestPrice - +item.details?.close) / +item.details?.close,
                instrument: item.details?.market + '|' + item.details?.securityType + '|' + item.details?.symbol,
                isShow: item.isShow,
                currency: item.config?.currency,
                market: item.details?.market,
                symbol: item.details?.symbol
            }"
        />
    </div>
</template>

<script setup>
import IndexTrendChild from './IndexTrendChild.Bull.vue'
import { $raise_fall_color } from '@/store/index.js'
import socket from '@/socket.js'
import { SOCKET_EVENTS } from '@/config/index.js'

defineProps({
    optional: Boolean,
})

const emits = defineEmits([ 'select' ])


const indexStore = useIndexStore(),
    { dispatch_refreshIndex } = indexStore,
    { $indexData } = storeToRefs(indexStore)

const onSelect = val => {
    emits('select', val)
}

useSocket(SOCKET_EVENTS.UPDATE_INDEX, dispatch_refreshIndex)
socket.on('Q', ({ data }) => {
    const [ market, securityType, latestTime, symbol, latestPrice, high, open, low, close, volume, amount ] = data.split('|')

    const instrument = [ market, securityType, symbol ].join('|')

    const chg = +latestPrice - +close
    if ($indexData.value[instrument]) {
        $indexData.value[instrument].details = {
            ...$indexData.value[instrument].details,
            chg,
            latestPrice: +latestPrice,
            gain: chg / +close,
            high: +high,
            open: +open,
            amount: +amount,
            low: +low,
            volume: +volume,
        }
        // $indexData.value[instrument].timeline.push({
        //     avgPrice: +latestPrice,
        //     price: +latestPrice,
        //     time: latestTime,
        //     volume,
        // })
    }
})

defineOptions({ name: 'IndexTrend' })
</script>

