<template>
    <c-header :title="t('_title')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-4"
            v-for="item in list"
            :key="item.id"
        >
            <template #title>
                <div class="flex-middle gap-1 text-xs">
                    <div class="marketBlock">
                        {{ stockMarketDict.get(item.market) }}
                    </div>

                    <div class="">
                        {{ item.symbolName }}
                    </div>

                    <span>{{ item.symbol }}</span>

                    <span class="text-xs" :class="[+item?.direction === 1 ? 'text-red-700': 'text-green-700']">
                            {{ direction(item) }}
                    </span>

                </div>
            </template>

            <template #extra>
                <span class="text-xs text-title">{{ t('_profit_lost') }}</span>
                <c-amount
                    colorful
                    :amount="item.winAmount"
                    :currency="item.currency"
                />
            </template>

            <c-description-group
                layout="vertical"
                :columns="3"
                :items="getItems(item)"
                :data-source="item"
                value-class="text-xs text-left"
            />
        </c-card>
    </c-record>
</template>

<script setup>
import { stockMarketDict } from '@/config/index.js'

const {
    list,
    refreshLoading,
    loadLoading,
    finished,
    onRefresh,
    onLoadMore,
} = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E6%8C%81%E4%BB%93%E7%9B%B8%E5%85%B3/indexPageUsingGET_1
    url: 'order/indexPage',
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '交易详情',
            _buy: '买入价格',
            _buy_time: '买入时间',
            _lever: '交易杠杆',
            _sell: '卖出价格',
            _sell_time: '卖出时间',
            _profit_lost: '盈亏',
            _fee: '手续费',
        },
        [LANGUAGE.zhHK]: {
            _title: '交易詳情',
            _buy: '買入價格',
            _buy_time: '买入时间',
            _lever: '交易杠杆',
            _sell: '卖出價格',
            _sell_time: '卖出时间',
            _profit_lost: '盈虧',
            _fee: '手續費',
        },
        [LANGUAGE.enUS]: {
            _title: 'Transaction Details',
            _buy: 'Buying Price',
            _buy_time: 'Purchase Time',
            _lever: 'Trading Leverage',
            _sell: 'Selling Price',
            _sell_time: 'Selling Time',
            _profit_lost: 'Profit and Loss',
            _fee: 'service fee',
        },
    },
})

const getItems = (v) => {
    const list = [
        {
            label: t('common.type'),
            value: 'tradeType',
            render: val => {
                const { label, color } = tradeTypeDict(val)
                return h(
                    'span',
                    {
                        class: `text-${color}`,
                    },
                    t(`dict.${label}`),
                )
            },
        },
        {
            label: t('_buy'),
            value: 'dealPrice',
        },
        {
            label: t('stock.transaction.quantity'),
            value: 'dealNum',
        },

        {
            label: t('_lever'),
            value: 'tradeUnit',
            render: val => t('contract.multiples', [ val ]),
        },
        // {
        //     label: t('_sell'),
        //     value: 'sellAvgPrice',
        // },
        // TODO:
        {
            label: t('_fee'),
            value: 'dealFee',
        },
    ]

    if (+v.direction === 2) {
        list.splice(1, 1, {
            label: t('_sell'),
            value: 'dealPrice',
        })
        list.push(
            {
                label: t('_sell_time'),
                value: 'updateTime',
            },
        )
    } else {
        list.push({
            label: t('_buy_time'),
            value: 'createTime',
        })
    }

    return list
}

const direction = computed(() => (v) => {
        return v.direction === 1 ? t('dict.买入') : t('dict.卖出')
    },
)

defineOptions({ name: 'Tnx-Info' })
</script>

<style scoped>
:deep(.c-description-group .c-description:nth-child(3n)) {
    text-align: right;
    flex: 1;
}

:deep(.c-description-group .c-description:nth-child(3n - 1)) {
    text-align: center;
}
</style>
