<!--期货列表-->
<template>
    <van-tabs
        data-aos="fade-left"
        shrink
        v-model:active="$futuresType"
        style="--van-tab-font-size: 12px;"
    >
        <van-tab
            v-for="({title, value}) in FUTURES_CONFIG"
            :key="value"
            :name="value"
            :title="t(`futures.${title}`)"
            :disabled="initial && refreshLoading"
        />
    </van-tabs>

    <div class="tab-container py-2.5">

        <c-table
            class="h-full"
            :columns
            :finished
            :data-source="list"
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            :initial-sort-status="defaultSortStatus"
            @row-click="handleRowClick"
        />
    </div>
</template>

<script setup lang="jsx">
import { FUTURES_CONFIG } from '@/config/index.js'
import { useFuturesColumns, useFuturesList } from '@/hooks/index.js'
import { useFuturesStore } from '@/store/futures.js'

const router = useRouter()
const futuresStore = useFuturesStore()
const { t } = useI18n()
const isSupportNight = ref(false)
const _cb = (bool) => {
    isSupportNight.value = bool
}
const columns = useFuturesColumns(_cb, computed(() => isSupportNight.value))
const { $futuresType } = storeToRefs(futuresStore)
const { dispatch_checkFutures, dispatch_futures_activeTab } = futuresStore

const defaultSortStatus = ref([
    0,
    2, // latestPrice 的列设置为 DESCENDING（高亮）
    0, // gain 默认无排序
])

const [
    { list, initial, refreshLoading, loadLoading, finished, onRefresh, onLoadMore, onFilter },
] = useFuturesList({
    params: computed(() => ({
        field: 'volume',
        order: 'DESC',
        isSupportNight: Boolean(isSupportNight.value) ? true : '',
        marketType: $futuresType.value,
        securityType: '4',
    })),
}, {
    paginationKeys: {
        current: 'pageNumber',
        pageSize: 'pageSize',
    },
})

const handleRowClick = (row) => {
    dispatch_checkFutures(row)
    dispatch_futures_activeTab('futures-details')
    router.push(`/futures/details`)
}

watch([ () => $futuresType.value, isSupportNight ], async (nv, ov) => {
    // if (nv !== ov) {
    await onRefresh()
    // }
})

defineOptions({ name: 'Futures-List' })
</script>


