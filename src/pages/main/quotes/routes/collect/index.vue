<template>
    <van-tabs
        data-aos="fade-left"
        shrink
        :active="$marketType"
        :before-change="onTabChange"
        style="--van-tab-font-size: 12px;"
    >
        <van-tab
            v-for="(_, symbol) in STOCK_CONFIG"
            :key="symbol"
            :name="symbol"
            :title="t(`stock.${symbol}`)"
            :disabled="initial && refreshLoading"
        />
    </van-tabs>

    <div class="tab-container py-2.5">
        <c-table
            class="h-full"
            :columns
            :finished
            :data-source="list"
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            @row-click="dispatch_checkStock"
        />
    </div>
</template>

<script setup>
import { STOCK_CONFIG } from '@/config/index.js'

const { dispatch_checkStock } = useStockStore(),
    { $marketType } = storeToRefs(useQuotesStore())

const columns = useStockColumns()

const { list, initial, refreshLoading, loadLoading, finished, onRefresh, onLoadMore, onFilter, res } = useStockCollect({
    params: computed(() => ({
        market: $marketType.value,
    })),
})

const onTabChange = async (market) => {
    $marketType.value = market
    await onRefresh()
    return true
}

const { t } = useI18n()

defineOptions({ name: 'quotes-collect' })
</script>
