<template>
    <div class="with-header-container__noPadding px-2.5">
        <!-- Use SkewTabs only for temp_coin template -->
        <SkewTabs
            v-if="isTempCoin"
            data-aos="fade-left"
            data-aos-delay="50"
            class="main-tab mt-2.5"
            v-model="$quotes_route"
            :tabs="tabs"
            :is-temp-coin="true"
            @change="handleTabChange"
        />

        <!-- Use original van-tabs for other templates -->
        <van-tabs
            v-else
            data-aos="fade-left"
            data-aos-delay="50"
            class="main-tab mt-2.5"
            type="card"
            title-inactive-color="var(--paragraph)"
            v-model:active="$quotes_route"
        >
            <van-tab
                v-if="showIndexSection"
                :title="$t('stock.title')"
                name="quotes-stock"
                to="/quotes"
            />

            <van-tab
                v-if="showIndexSection"
                :title="$t('stock.index')"
                name="quotes-index"
                to="/quotes/index"
            />
            <van-tab
                v-if="showFuturesSection"
                :title="$t('stock.futures')"
                name="quotes-futures"
                to="/quotes/futures"
            />
            <van-tab
                :title="$t('stock.collect')"
                name="quotes-collect"
                to="/quotes/collect"
            />
        </van-tabs>

        <div class="tab-container">
            <router-view/>
        </div>
    </div>
</template>

<script setup>
import { $quotes_route } from '@/store'
import SkewTabs from '@/components/SkewTabs/index.vue'
import { $theme } from '@/store/index.js'
import bgImageLight from '/skins/templates/_TEMPLATE_/_THEME_/bg_light2.png'
import bgImageDark from '/skins/templates/_TEMPLATE_/_THEME_/bg_dark2.png'

const { name } = useRoute()
const router = useRouter()
const { t: $t } = useI18n()

// System Config Store
const sysConfigStore = useSysConfigStore()
const { showIndexSection, showFuturesSection } = storeToRefs(sysConfigStore)

// Check if current template is temp_coin
const isTempCoin = computed(() => {
    return G_TEMPLATE === 'temp_coin'
})

// Define tabs based on configuration (only for SkewTabs)
const tabs = computed(() => {
    const tabList = []

    if (showIndexSection.value) {
        tabList.push({
            label: $t('stock.title'),
            value: 'quotes-stock',
            key: 'quotes-stock',
        })
        tabList.push({
            label: $t('stock.index'),
            value: 'quotes-index',
            key: 'quotes-index',
        })
    }

    if (showFuturesSection.value) {
        tabList.push({
            label: $t('stock.futures'),
            value: 'quotes-futures',
            key: 'quotes-futures',
        })
    }

    tabList.push({
        label: $t('stock.collect'),
        value: 'quotes-collect',
        key: 'quotes-collect',
    })

    return tabList
})

const handleTabChange = (tab) => {
    const routeMap = {
        'quotes-stock': '/quotes',
        'quotes-index': '/quotes/index',
        'quotes-futures': '/quotes/futures',
        'quotes-collect': '/quotes/collect',
    }

    if (routeMap[tab.value]) {
        router.push(routeMap[tab.value])
    }
}

if ($quotes_route.value !== name) {
    $quotes_route.value = name
}

onMounted(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        appEl.style.background = `var(--page) url(${$theme.value === 'light' ? bgImageLight : bgImageDark}) no-repeat top center`
        appEl.style.backgroundSize = 'contain, auto'
    }
})

onBeforeUnmount(() => {
    if (G_TEMPLATE === 'temp_bull') {
        const appEl = document.querySelector('#app')
        if (appEl) {
            appEl.style.background = ''
            appEl.style.backgroundSize = ''
            appEl.style.backgroundPositionY = ''
        }
    }
})

defineOptions({ name: 'transaction' })
</script>

<style scoped>
/* Styles for van-tabs (only applied when not temp_coin) */
:deep(.main-tab > .van-tabs__wrap) {
    background: var(--card);
    border-radius: var(--van-tabs__wrap);
}

:deep(.main-tab .van-tabs__nav--card) {
    margin: 0;
    border-radius: var(--van-tabs__nav--card);
    border: none;
    justify-content: space-between;
}

:deep(.main-tab .van-tabs__nav--card > .van-tab) {
    width: 80px;
    flex: none;
    border: none;
    border-radius: var(--van-tab);
}
</style>
