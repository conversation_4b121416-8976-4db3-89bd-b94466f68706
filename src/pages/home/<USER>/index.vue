<template>
    <div class="main flex flex-col items-center">
        <img src="./logo.png" alt="LOGO" class="w-45 mt-[30%]">
        <img src="./slogan.png" alt="SLOGAN" class="w-[209px] mt-10">
        <div class="w-full flex justify-center">
            <van-button size="large" @click="handleClick"
                        class="mt-15 w-3/4 bg-[#2662FF] text-white rounded-lg text-xl">
                请点击继续访问
            </van-button>
        </div>
    </div>
</template>

<script setup>
const router = useRouter()
const handleClick = () => {
    router.push('/home')
}
</script>

<style scoped>
.main {
    background: #f0f0f0 url('./bg_img.png') no-repeat center center fixed;
    background-size: cover;
    height: 100%;
}
</style>