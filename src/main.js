import 'virtual:svg-icons-register'
import AOS from 'aos'
import 'aos/dist/aos.css'
import WujieVue from 'wujie-vue3'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import '@/socket.js'
import App from '@/App.vue'
import router from '@/router'
import components from '@/components'

// skins/themes/golden/index.css
// vite 不支持动态导入, 打包时使用 vite-plugin-replace 把_THEME_ 替换成.env中VITE_UI_COLOR_SCHEME的值
import '@themes/_THEME_/index.css'

// Tailwind v4 compatibility layer
import '@/v4-compatibility.css'

import { $raise_fall_color } from '@/store'
// import VConsole from 'vconsole'
//
// if (import.meta.env.MODE !== 'prod') {
//     const vConsole = new VConsole()
// }

import ResizeObserver from 'resize-observer-polyfill'

if (!window.ResizeObserver) {
    window.ResizeObserver = ResizeObserver
}


const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

document.documentElement.className = $raise_fall_color.value

AOS.init({
    offset: 0, // 从原始触发点开始的偏移量（以像素为单位）

    // https://github.com/michalsnik/aos
    // 全局设置：
    // disable: false, // 接受以下值：'phone'（手机），'tablet'（平板），'mobile'（移动设备），布尔值，表达式或函数，用于禁用 AOS
    // startEvent: 'DOMContentLoaded', // 初始化 AOS 时，在 document 上触发的事件名称
    // initClassName: 'aos-init', // 初始化后应用的类名
    // animatedClassName: 'aos-animate', // 动画完成后应用的类名
    // useClassNames: false, // 如果为 true，将在滚动时将 `data-aos` 的内容添加为类名
    // disableMutationObserver: false, // 禁用自动检测 DOM 的变化（高级设置）
    // debounceDelay: 50, // 调整窗口大小时使用的去抖动延迟（高级设置）
    // throttleDelay: 99, // 页面滚动时使用的节流延迟（高级设置）
    // delay: 0, // 动画延迟时间，取值范围为 0 到 3000，步长为 50ms
    // duration: 1000, // 动画持续时间，取值范围为 0 到 3000，步长为 50ms
    // easing: 'ease', // AOS 动画的默认缓动效果
    // once: false, // 动画是否仅在向下滚动时触发一次
    // mirror: true, // 元素在滚动过后是否再次触发动画
    // anchorPlacement: 'top-bottom', // 定义触发动画时元素相对于窗口的位置
})

createApp(App)
    .use(i18n)
    .use(pinia)
    .use(router)
    .use(components)
    .use(WujieVue)
    .mount('#app')
