/* Tailwind v4 Compatibility Layer for v3.4 */

/* Text size utilities with !important (v4 style) */
.text-xs\! {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

.text-sm\! {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.text-base\! {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

.text-lg\! {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}

.text-xl\! {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}

.text-2xl\! {
  font-size: 1.5rem !important;
  line-height: 2rem !important;
}

.text-3xl\! {
  font-size: 1.875rem !important;
  line-height: 2.25rem !important;
}

.text-4xl\! {
  font-size: 2.25rem !important;
  line-height: 2.5rem !important;
}

.text-5xl\! {
  font-size: 3rem !important;
  line-height: 1 !important;
}

.text-6xl\! {
  font-size: 3.75rem !important;
  line-height: 1 !important;
}

.text-7xl\! {
  font-size: 4.5rem !important;
  line-height: 1 !important;
}

.text-8xl\! {
  font-size: 6rem !important;
  line-height: 1 !important;
}

.text-9xl\! {
  font-size: 8rem !important;
  line-height: 1 !important;
}

/* CSS Custom Properties support (v4 style) */
.h-\(--header-height\) {
  height: var(--header-height) !important;
}

.w-\(--header-height\) {
  width: var(--header-height) !important;
}

.min-h-\(--header-height\) {
  min-height: var(--header-height) !important;
}

.max-h-\(--header-height\) {
  max-height: var(--header-height) !important;
}

.h-\(--primary\) {
  height: var(--primary) !important;
}

.w-\(--primary\) {
  width: var(--primary) !important;
}

.h-\(--bg\) {
  height: var(--bg) !important;
}

.w-\(--bg\) {
  width: var(--bg) !important;
}

.h-\(--text\) {
  height: var(--text) !important;
}

.w-\(--text\) {
  width: var(--text) !important;
}

.h-\(--title\) {
  height: var(--title) !important;
}

.w-\(--title\) {
  width: var(--title) !important;
}

.h-\(--border\) {
  height: var(--border) !important;
}

.w-\(--border\) {
  width: var(--border) !important;
}

.h-\(--input\) {
  height: var(--input) !important;
}

.w-\(--input\) {
  width: var(--input) !important;
}

.h-\(--card\) {
  height: var(--card) !important;
}

.w-\(--card\) {
  width: var(--card) !important;
}

.h-\(--active\) {
  height: var(--active) !important;
}

.w-\(--active\) {
  width: var(--active) !important;
}

.h-\(--link\) {
  height: var(--link) !important;
}

.w-\(--link\) {
  width: var(--link) !important;
}

.h-\(--special\) {
  height: var(--special) !important;
}

.w-\(--special\) {
  width: var(--special) !important;
}

.h-\(--tag\) {
  height: var(--tag) !important;
}

.w-\(--tag\) {
  width: var(--tag) !important;
}

.h-\(--red\) {
  height: var(--red) !important;
}

.w-\(--red\) {
  width: var(--red) !important;
}

.h-\(--green\) {
  height: var(--green) !important;
}

.w-\(--green\) {
  width: var(--green) !important;
}

.h-\(--raise\) {
  height: var(--raise) !important;
}

.w-\(--raise\) {
  width: var(--raise) !important;
}

.h-\(--fall\) {
  height: var(--fall) !important;
}

.w-\(--fall\) {
  width: var(--fall) !important;
}

.h-\(--page\) {
  height: var(--page) !important;
}

.w-\(--page\) {
  width: var(--page) !important;
}

.h-\(--paragraph\) {
  height: var(--paragraph) !important;
}

.w-\(--paragraph\) {
  width: var(--paragraph) !important;
}

.h-\(--controller_bg\) {
  height: var(--controller_bg) !important;
}

.w-\(--controller_bg\) {
  width: var(--controller_bg) !important;
}

.h-\(--red-text\) {
  height: var(--red-text) !important;
}

.w-\(--red-text\) {
  width: var(--red-text) !important;
}

.h-\(--futures-red\) {
  height: var(--futures-red) !important;
}

.w-\(--futures-red\) {
  width: var(--futures-red) !important;
}

.h-\(--futures-green\) {
  height: var(--futures-green) !important;
}

.w-\(--futures-green\) {
  width: var(--futures-green) !important;
}

.h-\(--futures-switch\) {
  height: var(--futures-switch) !important;
}

.w-\(--futures-switch\) {
  width: var(--futures-switch) !important;
}

/* Enhanced utilities (v4 style) */
.text-pretty {
  text-wrap: pretty;
}

.text-balance {
  text-wrap: balance;
}

.bg-stripes {
  background-image: repeating-linear-gradient(45deg, transparent, transparent 2px, currentColor 2px, currentColor 4px);
  background-size: 8px 8px;
}

.scrollbar-none {
  scrollbar-width: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.forced-colors-adjust-auto {
  forced-color-adjust: auto;
}

.forced-colors-adjust-none {
  forced-color-adjust: none;
}

/* Size utilities (v4 style) */
.size-auto {
  width: auto;
  height: auto;
}

.size-full {
  width: 100%;
  height: 100%;
}

.size-screen {
  width: 100vw;
  height: 100vh;
}

.size-svw {
  width: 100svw;
  height: 100svh;
}

.size-lvw {
  width: 100lvw;
  height: 100lvh;
}

.size-dvw {
  width: 100dvw;
  height: 100dvh;
}

.size-min {
  width: min-content;
  height: min-content;
}

.size-max {
  width: max-content;
  height: max-content;
}

.size-fit {
  width: fit-content;
  height: fit-content;
} 