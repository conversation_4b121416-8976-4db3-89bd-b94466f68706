<template>
    <div class="main-layout h-full">
        <div class="main-container overflow-auto">
            <router-view name="ActionsBar"/>
            <router-view/>
        </div>

        <div
            class="navbar bg-bg h-14 flex-middle justify-around rounded-tl-xl rounded-tr-xl text-xs"
            data-aos="fade-up"
        >
            <router-link
                class="w-1/5 text-center h-full flex flex-col justify-center items-center"
                v-for="({ title, name, icon }) in navBar"
                :key="name"
                :to="{ name }"
                exact-active-class="active"
                replace
            >
                <c-icon
                    prefix="navbar"
                    :name="`${icon ?? name}${$route.name.includes(name) ? '_active' : `_${$theme}`}`"
                    class="mx-auto mb-1"
                />

                <div>{{ title }}
                </div>
            </router-link>
        </div>
    </div>

    <!-- 弹窗公告 -->
    <Notice/>
    <!-- 弹窗公告 -->
</template>

<script setup>
import { $theme } from '@/store/index.js'
import Notice from '../components/Notice_new.vue'

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _home: '首页',
            _quotes: '行情',
            _account: '账户',
            _mine: '我的',
        },
        [LANGUAGE.zhHK]: {
            _home: '首頁',
            _quotes: '行情',
            _account: '帳戶',
            _mine: '我的',
        },
        [LANGUAGE.enUS]: {
            _home: 'home',
            _quotes: 'Quotes',
            _account: 'Account',
            _mine: 'mine',
        },
    },
})

const navBar = computed(() => [
    {
        title: t('_home'),
        name: 'home',
    },
    {
        title: t('_account'),
        name: 'account',
    },
    {
        title: t('_quotes'),
        name: $quotes_route.value,
        icon: 'quotes',
    },
    {
        title: t('header.activity'),
        name: 'activity',
    },
    {
        title: t('_mine'),
        name: 'mine',
    },
])

defineOptions({ name: 'mainLayout' })
</script>

<style scoped>
.main-container {
    /* Navbar 56 */
    height: calc(100% - 56px);
}

.navbar {
    box-shadow: 0 -4px 12px 0 var(--shadow);
}

.van-theme-dark .active {
    color: var(--select);
}

.van-theme-light .active {
    color: var(--select);
}
</style>
