<template>
    <van-popup
        style="width: min(90%, 640px * .9); background-color: transparent;"
        :close-on-click-overlay="false"
        :destroy-on-close="true"
        v-model:show="$noticePopup"
    >
        <van-swipe>
            <van-swipe-item
                v-for="({ id, title, imageUrl, jumpUrl, jumpType }) in $notice.popup"
                :key="id"
            >
                <c-image-box
                    class="flex flex-col"
                    :source="bg"
                >
                    <div
                        class="w-1/2 h-10 flex-center gap-1.5 font-semibold text-primary whitespace-nowrap"
                        :class="{ 'text-xl': isCn }"
                    >
                        <img
                            src="../assets/icon.png"
                            alt="icon"
                            class="h-8"
                        >
                        <span>{{ t('_title') }}</span>
                    </div>

                    <div class="flex-1 flex flex-col px-8 pb-8" @click="utils_jump(jumpType, jumpUrl)">
                        <div class="leading-12 text-center text-paragraph font-semibold">
                            {{ title }}
                        </div>

                        <div class="p-2.5">
                            <img
                                class="w-full flex-1"
                                :src="imageUrl"
                                :alt="title"
                            >
                        </div>
                    </div>
                </c-image-box>
            </van-swipe-item>

            <template #indicator="{ active, total }">
                <div class="mt-2.5 flex-center gap-1.5">
                    <div
                        class="size-1.5 rounded-full"
                        v-for="n in total"
                        :key="n"
                        :class="[ active === (n - 1) ? 'bg-active w-5' : 'bg-card' ]"
                    />
                </div>
            </template>
        </van-swipe>

        <div class="mt-4 text-center" @click="$noticePopup = false">
            <van-icon
                class="block!"
                name="close"
                size="32"
            />
        </div>
    </van-popup>
</template>

<script setup>
import bg from '../assets/notice_bg.png'
import { utils_jump } from '@/utils'

const { $noticePopup, $notice } = storeToRefs(useNoticeStore())

const { t, locale } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '最新活动',
        },
        [LANGUAGE.zhHK]: {
            _title: '最新活动',
        },
        [LANGUAGE.enUS]: {
            _title: 'Latest activities',
        },
    },
})

const isCn = locale.value === LANGUAGE.zhCN

// 弹窗公告
defineOptions({ name: 'NoticeNew' })
</script>

<style scoped>
</style>
