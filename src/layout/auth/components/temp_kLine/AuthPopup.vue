<template>
    <div
        class="auth-popup h-full rounded-tl-xl rounded-tr-xl py-5 px-5 overflow-auto"
        data-aos="fade-up"
        data-aos-delay="150"
    >
        <slot/>
    </div>
</template>

<script setup>
defineOptions({ name: 'AuthPopup' })
</script>

<style scoped>
.van-theme-light .auth-popup {
    background: var(--van-white);
    box-shadow: 0 -2px 30px 0 rgba(53, 70, 119, .08);
}

.van-theme-dark .auth-popup {
    background: rgba(38, 43, 52, .6);
    box-shadow: 0 -4px 30px 0 rgba(11, 18, 36, .2);
    backdrop-filter: blur(4px)
}
</style>
