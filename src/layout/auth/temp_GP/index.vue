<template>
    <div class="h-full" :style="backgroundStyle">
        <div class="h-12 px-4 flex-between" data-aos="fade-up">
            <div class="w-5">
                <van-icon
                    class="text-xl"
                    name="arrow-left"
                    @click="onBack"
                />
            </div>

            <img
                class="w-[100px] h-8"
                alt="logo"
                :src="$logo"
            />

            <van-popover
                placement="bottom-end"
                :show-arrow="false"
            >
                <template #reference>
                    <c-icon prefix="auth" name="global"/>
                </template>

                <div
                    class="flex-middle gap-2 p-2 text-sm"
                    v-for="({ title, key, active }) in langActions"
                    @click="onSelect(key)"
                >
                    <c-icon :name="key"/>

                    <div :class="{ 'text-active': active }">
                        {{ title }}
                    </div>
                </div>
            </van-popover>
        </div>

        <div class="auth-container flex flex-col">
            <div class="h-[200px] flex-between pl-6 mb-auto">
                <div
                    class="text-2xl text-title leading-10 whitespace-pre-line"
                    data-aos="fade-up"
                    data-aos-delay="50"
                >
                    {{ $globalConfig.slogan?.[$i18n.locale] ?? '' }}
                </div>

                <img
                    src="/skins/templates/_TEMPLATE_/_THEME_/cover.png"
                    alt="cover"
                    class="h-[200px]"
                    data-aos="fade-up"
                    data-aos-delay="100"
                >
            </div>

            <router-view/>
        </div>
    </div>
</template>

<script setup>
import { useAuthLayout } from '../composables/useAuthLayout.js'
import { $theme } from '@/store/global.js'

const {
    $globalConfig,
    $logo,
    langActions,
    onBack,
    onSelect,
} = useAuthLayout()

const backgroundStyle = computed(() => {
    const bgImage = `/skins/templates/${G_TEMPLATE}/${G_THEME}/cover_${$theme.value}.png`
    const gradient =
        $theme.value === 'light'
            ? 'linear-gradient(270deg, #E1E8FF 0%, #E6E8F7 49.5%, #FFFFFF 100%)'
            : 'linear-gradient(270deg, #13161B 0%, #1B2028 49.5%, #20252E 100%)'

    return {
        backgroundImage: `url("${bgImage}"), ${gradient}`,
        backgroundPosition: '-48px -58px, 0 0',
        backgroundSize: '280px, 100%',
        backgroundRepeat: 'no-repeat',
    }
})

defineOptions({ name: 'authLayout' })
</script>

<style scoped>
.auth-container {
    /*
        Header 48
    */
    height: calc(100% - 48px);
}
</style>
