import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { unref } from 'vue'

export function useAuthLayout() {
    const { $globalConfig, $logo } = storeToRefs(useGlobalStore())

    const { langActions, optionConfig } = useSystemSetting(SETTING_TYPES.LANGUAGE)

    const { currentRoute, replace, back } = useRouter()

    const onBack = () => {
        if (currentRoute.value.name === 'login') {
            replace('/home')
        } else {
            back()
        }
    }

    const onSelect = (lang) => {
        unref(optionConfig).onSelect(lang)
    }

    return {
        $globalConfig,
        $logo,
        langActions,
        optionConfig,
        onBack,
        onSelect,
    }
}
