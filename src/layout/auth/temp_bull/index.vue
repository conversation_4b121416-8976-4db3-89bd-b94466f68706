<template>
    <div class="h-full">
        <div class="h-[350px] relative" :style="backgroundStyle">
            <div class="h-12 px-4 bg-container flex-between" data-aos="fade-up">
                <!-- 返回按钮 -->
                <div class="w-5 flex items-center">
                    <van-icon
                        class="text-xl"
                        name="arrow-left"
                        color="#F15302"
                        @click="onBack"
                    />
                </div>

                <!-- LOGO 读取的后台配置 -->
                <img
                    class="w-[100px] h-8"
                    alt="logo"
                    :src="$logo"
                />

                <!-- 语言切换 -->
                <van-popover
                    placement="bottom-end"
                    :show-arrow="false"
                >
                    <template #reference>
                        <c-icon prefix="auth" name="global"/>
                    </template>

                    <div
                        class="flex-middle gap-2 p-2 text-sm text-regular"
                        v-for="({ title, key, active }) in langActions"
                        @click="onSelect(key)"
                    >
                        <c-icon :name="key" :color="[active ? 'var(--active)' : 'var(--text-regular)']"/>

                        <div :class="{ 'text-active': active }">
                            {{ title }}
                        </div>
                    </div>
                </van-popover>
            </div>

            <div class="h-[40px] flex-between flex justify-center absolute bottom-2.5 left-0 right-0">
                <div
                    class="text-[24px] text-[#FF2020] bold"
                    data-aos="fade-up"
                    data-aos-delay="50"
                >
                    {{ slogan }}
                </div>
            </div>
        </div>
        <div class="flex flex-col h-[calc(100%-350px)]">
            <router-view/>
        </div>
    </div>
</template>

<script setup>
import { useAuthLayout } from '../composables/useAuthLayout.js'
import bgImage from '/skins/templates/_TEMPLATE_/_THEME_/cover.png'

const i18n = useI18n()

const {
    $globalConfig,
    $logo,
    langActions,
    onBack,
    onSelect,
} = useAuthLayout()

const slogan = computed(() => {
    return $globalConfig.value.slogan?.[i18n.locale.value]?.replace(/\n/g, ' ')
})

const backgroundStyle = computed(() => {
    return {
        backgroundImage: `url("${bgImage}")`,
        backgroundSize: 'cover, contain',
        backgroundRepeat: 'no-repeat',
        height: '350px',
    }
})

defineOptions({ name: 'authLayout' })
</script>
