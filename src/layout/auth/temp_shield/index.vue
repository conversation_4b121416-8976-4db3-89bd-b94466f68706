<template>
    <div class="h-full" :style="backgroundStyle">
        <div class="h-[337px]">
            <div class="h-12 px-4 bg-container flex-between" data-aos="fade-up">
                <!-- 返回按钮 -->
                <div class="w-5 flex items-center">
                    <van-icon
                        class="text-xl"
                        name="arrow-left"
                        @click="onBack"
                    />
                </div>

                <!-- LOGO 读取的后台配置 -->
                <img
                    class="w-[100px] h-8"
                    alt="logo"
                    :src="$logo"
                />

                <!-- 语言切换 -->
                <van-popover
                    placement="bottom-end"
                    :show-arrow="false"
                >
                    <template #reference>
                        <c-icon prefix="auth" name="global"/>
                    </template>

                    <div
                        class="flex-middle gap-2 p-2 text-sm text-regular"
                        v-for="({ title, key, active }) in langActions"
                        @click="onSelect(key)"
                    >
                        <c-icon :name="key" :color="[active ? 'var(--active)' : 'var(--text-regular)']"/>

                        <div :class="{ 'text-active': active }">
                            {{ title }}
                        </div>
                    </div>
                </van-popover>
            </div>
            <img class="w-56 h-56 m-auto mt-15 scale-120" src="/skins/templates/_TEMPLATE_/_THEME_/shield.png" alt="">

            <div class="h-[100px] flex-between pl-6 absolute top-15">
                <div
                    class="text-2xl text-title leading-10 whitespace-pre-line"
                    data-aos="fade-up"
                    data-aos-delay="50"
                >
                    {{ $globalConfig.slogan?.[$i18n.locale] ?? '' }}
                </div>
            </div>
        </div>
        <div class="flex flex-col h-[calc(100%-337px)]">
            <router-view/>
        </div>
    </div>
</template>

<script setup>
import { useAuthLayout } from '../composables/useAuthLayout.js'
import { $theme } from '@/store/index.js'
import bgImage from '/skins/templates/_TEMPLATE_/_THEME_/cover.png'


const {
    $globalConfig,
    $logo,
    langActions,
    onBack,
    onSelect,
} = useAuthLayout()

const backgroundStyle = computed(() => {
    const gradient =
        $theme.value === 'light'
            ? 'linear-gradient(270deg, #E1E8FF 0%, #E6E8F7 49.5%, #FFFFFF 100%)'
            : 'linear-gradient(270deg, #13161B 0%, #1B2028 49.5%, #20252E 100%)'

    return {
        backgroundImage: `url("${bgImage}"), ${gradient}`,
        // backgroundPosition: '-48px -58px, 0 0',
        backgroundSize: '100%, 100%',
        backgroundRepeat: 'no-repeat',
    }
})

defineOptions({ name: 'authLayout' })
</script>
