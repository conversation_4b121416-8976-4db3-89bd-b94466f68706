import _ from 'lodash'
import dayjs from 'dayjs'

const t = (...args) => i18n.global.t(...args)
// 股票配置
export const STOCK_CONFIG = {
    CN: {
        symbol: 'CN', markets: 'SZSE,SSE', plate: [ // 深证
            'SZSE_PLATE', // 上证
            'SSE_PLATE', // 科创板
            'STAR_MARKET', // 创业板
            'SECOND_BOARD' ], currency: CURRENCY.CNY,
    }, HK: {
        symbol: 'HK', markets: 'HKEX', plate: [ // 创业板
            'CREATE_PLATE', // 主板
            'MAIN_PLATE' ], currency: CURRENCY.HKD,
    }, US: {
        symbol: 'US', markets: 'US', plate: [ // 中概股
            'US_CHINA', // 明星股
            'US_STAR' ], currency: CURRENCY.USD,
    },
}

export const FUTURES_CONFIG = [ {
    title: 'dalian_futures_exchange', value: 'DCE',
}, {
    title: 'zhengzhou_futures_exchange', value: 'CZCE',
}, {
    title: 'shanghai_futures_exchange', value: 'SHFE',
}, {
    title: 'shanghai_energy_exchange', value: 'INE',
}, {
    title: 'china_futures_exchange', value: 'CFFEX',
} ]


export const STOCK_ROUTE = {
    DETAILS: 'details',
    DETAILS_NAME: 'stock-details',
    TRANSACTION: 'transaction',
    TRANSACTION_STOCK: 'stock-transaction',
    TRANSACTION_INDEX: 'quotes-index',
}

// 股票市场字典表
export const stockMarketDict = new Map([ [ 'SZSE', 'SZ' ], [ 'SSE', 'SH' ], [ 'HKEX', 'HK' ], [ 'US', 'US' ] ])
// 股票国家字典表
export const stockCountryDict = new Map([ [ 'SZSE', 'CN' ], [ 'SSE', 'CN' ], [ 'HKEX', 'HK' ], [ 'US', 'US' ] ])

// 股票交易方向选项配置
export const tradeDirectionOptions = [ { label: '开仓', value: 1, color: 'raise' }, { label: '平仓', value: 2, color: 'fall' } ]
// 股票交易方向字典表
export const tradeDirectionDict = (direction) => {
    return _.find(tradeDirectionOptions, { value: direction })
}

// 股票交易类型选项配置
export const tradeTypeOptions = [ { label: '平多', value: 1, color: 'raise' }, { label: '平空', value: 2, color: 'fall' } ]
// 股票交易类型选字典表
export const tradeTypeDict = (type) => {
    return _.find(tradeTypeOptions, { value: type })
}

// 图表类型
export const CHART_TYPES = {
    // 分时
    REAL_TIME: 'realtime_day', // 分时 - 五日
    FIVE_DAYS: 'realtime_5day', // 日K
    DAILY: 'kline_day', // 周K
    WEEKLY: 'kline_week', // 月K
    MONTHLY: 'kline_month', // 年K
    YEARLY: 'kline_year', // 1分
    ONE_MINUTE: 'kline_1min', // 5分
    FIVE_MINUTES: 'kline_5min', // 15分
    FIFTEEN_MINUTES: 'kline_15min', // 30分
    THIRTY_MINUTES: 'kline_30min',
}

const today = dayjs().format(TIME_FORMAT.YMD)
const generateMinuteRange = (startTime, endTime) => {
    const start = dayjs(`${today} ${startTime}`), end = dayjs(`${today} ${endTime}`), minutes = end.diff(start, 'minute') + 1

    return _.range(minutes).map((index) => start.add(index, 'minute').format(TIME_FORMAT.Hm))
}

const morningRange = generateMinuteRange('09:30', '11:29'), afternoonRange = generateMinuteRange('13:01', '15:00')

// 分时时间段数据
export const MINUTES_RANGE = _.concat(morningRange, '11:30/13:00', afternoonRange)
export const HK_MINUTES_RANGE = _.concat(generateMinuteRange('09:30', '11:59'), '12:00/13:00', generateMinuteRange('13:01', '16:00'))
export const US_MINUTES_RANGE = _.concat(generateMinuteRange('09:30', '16:00'))
