import Wujie from 'wujie-vue3'

import router from '@/router'
import socket from '@/socket.js'
import i18n from '@/i18n/index.js'

export const logout = (option) => {
    Wujie.destroyApp('service')

    const { redirect = true, replace } = option ?? {}

    const { dispatch_resetProfile } = useProfileStore(),
        { dispatch_resetAccount } = useAccountStore()

    $token.value = ''
    dispatch_resetProfile()
    dispatch_resetAccount()

    if (redirect) router.push({ name: 'login', replace })
}

export const useLogout = () => {
    const { t } = useI18n({
        useScope: 'local',
        messages: {
            [LANGUAGE.zhCN]: {
                _logout_confirm: '是否确认退出登录？',
                _successfully: '退出成功',
            },
            [LANGUAGE.zhHK]: {
                _logout_confirm: '是否确认退出登录？',
                _successfully: '退出成功',
            },
            [LANGUAGE.enUS]: {
                _logout_confirm: 'Are sure log out?',
                _successfully: 'Log out successfully',
            },
        },
    })

    const [ onLogout, loading ] = useFetchLoading(async () => {
        try {
            await api_post({
                // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF%E4%BC%9A%E5%91%98/logoutUsingPOST_1
                url: '/member/logout',
            })
        } finally {
            showSuccessToast(t('_successfully'))
            logout({ replace: true })

        }
    })

    const onLogoutConfirm = async () => {
        await showConfirmDialog({
            title: t('_logout_confirm'),
            confirmButtonColor: 'red',
        })
        await onLogout()
    }

    return {
        loading,
        onLogoutConfirm,
    }
}

socket.on('auth', async (res) => {
    const { dispatch_resetProfile } = useProfileStore(),
        { dispatch_resetAccount } = useAccountStore()

    if (res.code === 401) {
        $token.value = ''
        dispatch_resetProfile()
        dispatch_resetAccount()

        await showDialog({
            title: i18n.global.t('auth.re_login'),
        })
        await router.push({ name: 'login', replace: true })
    }
})

export const OTP_VERIFY = {
    ACCOUNT: 'account',
    MOBILE: 'mobile',
}

export const OTP_TYPES = {
    LOGIN: 'login',
    REGISTER: 'register',
    UPDATE_PASSWORD: 'updatePassword',
    BIND: 'bindCard',
    UPDATE_MOBILE: 'updateMobile',
}

export const useOtp = ({ type, config, name }, isNeed) => {
    const { t } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _resend: '重新发送',
                _sent_successfully: '短信验证码发送成功',
            },
            [LANGUAGE.zhHK]: {
                _resend: '重新发送',
                _sent_successfully: '短信验证码发送成功',
            },
            [LANGUAGE.enUS]: {
                _resend: 'Resend',
                _sent_successfully: 'SMS OTP sent successfully',
            },
        },
    })
    const { onAwaitCaptcha, validate } = useCaptcha()
    const { countdown, start } = _useCountdown(name ?? type, config)

    const [ onSendOtp, otpLoading ] = useFetchLoading(async (mobile) => {

        if (!REGULAR.MOBILE.test(mobile)) {
            showFailToast(t('profile.mobile_error'))
            return Promise.reject()
        }

        // 开启网易验证
        if (isNeed?.value) await onAwaitCaptcha()

        const els = document.querySelectorAll('.yidun_popup--light.yidun_popup.yidun_popup--size-small.yidun_popup--append')
        if (els?.length) {
            els?.forEach(el => {
                el.style.display = 'none'
            })
        }
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%82%A1%E6%8C%87%E9%85%8D%E7%BD%AE/getConfigUsingGET_1
            url: '/sms/sendMsg',
            params: {
                mobile,
                sendType: type,
                validate: validate.value,
            },
        })
        start()
        showSuccessToast(t('_sent_successfully'))
    })

    const otpText = computed(() => countdown.value ? `${t('_resend')}(${countdown.value}s)` : t('auth.otp_get')),
        otpDisabled = computed(() => !!countdown.value)

    return {
        otpLoading,
        otpText,
        otpDisabled,
        onSendOtp,
    }
}
