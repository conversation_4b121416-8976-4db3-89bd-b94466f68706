// 获取期货交易状态

export function useFuturesStatus() {
    const futuresStore = useFuturesStore()
    const {
        $futuresType, $minuteType, $currentFutures,
    } = storeToRefs(futuresStore)
    const result = ref(null)

    // 获取期货的交易状态
    async function fetchFuturesStatus() {
        const data = await api_get({
            url: '/futures/market/getFutureOpenTime', params: {
                market: $currentFutures.value?.market, productCode: $currentFutures.value?.productCode,
            },
        })
        result.value = data
    }

    const intervalStatusId = setInterval(fetchFuturesStatus, 3500)
    onMounted(fetchFuturesStatus)

    onBeforeUnmount(() => {
        clearInterval(intervalStatusId)
    })
    return {
        futuresStatus: result,
        $currentFutures,
    }
}

export const futuresInfoInitial = {
    symbol: '',
    industryPlate: '',
    precision: 0,
    high52w: 0,
    peStatic: 0,
    peLyr: 0,
    securityStatus: 0,
    gain: 0,
    amplitude: 0,
    high: 0,
    low: 0,
    floatShare: 0,
    VWAP: 0,
    dividend: 0,
    currency: '',
    tag: '',
    close: 0,
    turnover: 0,
    latestPrice: 0,
    peTtm: 0,
    amount: 0,
    chg: 0,
    lotSize: 1,
    marketValue: 0,
    dividendRate: 0,
    priceUpLimited: 0,
    priceDownLimited: 0,
    totalShares: 0,
    market: '',
    volume: 0,
    pb: 0,
    securityType: '',
    low52w: 0,
    name: '',
    latestTime: 0,
    open: 0,
}

export const onGetFuturesInfo = async (params) => {
    return await api_get({
        url: '/futures/market/stockInfo', params, options: {
            headers: {
                cancellable: false,
            },
        },
    })
}
