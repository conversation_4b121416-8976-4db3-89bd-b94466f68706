import currency from 'currency.js'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import _ from 'lodash'
import CryptoJS from 'crypto-js'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.tz.setDefault('Asia/Shanghai')

import router from '@/router.js'

/**
 * @function utils_time
 * @description 时间处理工具函数
 * */
export const utils_time = (time, format = TIME_FORMAT.FULL) => {
    const _time = dayjs.tz(time)
    return _time.isValid() ? _time.format(format) : '-'
}

/**
 * @function utils_currency
 * @description 货币处理工具函数
 * @param amount {number | string} 金额
 * @param [config] {object} 配置
 * */
export const utils_currency = (amount, config) => !_.isNaN(+amount) ? currency(amount, { symbol: '', ...config }).format() : 0

/**
 * @function utils_amount_color
 * @description 涨跌颜色判断工具函数
 * @param amount {number} 金额
 * @param [config] {object} 配置
 * */
export const utils_amount_color = (amount, config) => {
    const { raise = 'text-raise', fall = 'text-fall', flat = 'text-text' } = config ?? {}

    if (amount > 0) {
        return raise
    } else if (amount < 0) {
        return fall
    } else {
        return flat
    }
}

/**
 * @function utils_theme
 * @description 获取当前主题工具函数
 * @return string
 * */
export const utils_theme = () => {
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches

    let theme = THEME_CONFIG.LIGHT
    if (systemTheme) {
        theme = THEME_CONFIG.DARK // 深色模式
    }

    return theme
}

/**
 * @function utils_mobile_privacy
 * @description 电话号保密处理工具函数
 * @param mobile {string} 手机号
 * @return string
 * */
export const utils_mobile_privacy = (mobile) => {
    return `${mobile.slice(0, 3)}****${mobile.slice(-4)}`
}

/**
 * @function utils_format_options
 * @description 给选项配置格式化工具函数
 * @param original {Array<any>} 链接地址
 * @param fileNames {object} a标签属性
 * @param extra {function} 扩展数据
 * @return Array<any>
 * */
export const utils_format_options = (original, fileNames, extra) => {
    return original?.map(e => {
        _.entries(fileNames).forEach(obj => {
            const [ key, value ] = obj
            e[key] = _.isFunction(value) ? value(e) : e[value]
        })

        return {
            ...e,
            ...extra?.(e),
        }
    }) ?? []
}

/**
 * @function utils_link
 * @description 跳转链接工具函数
 * @param href {string} 链接地址
 * @param [config] {object} a标签属性
 * */
export const utils_link = (href, config) => {
    const { target = '_blank' } = config ?? {}
    const anchor = document.createElement('a')
    anchor.href = href
    anchor.target = target
    anchor.rel = 'noopener'
    anchor.click()
}
// 下载使用
export const utils_link2 = (href, config) => {
    const { target = '_blank' } = config ?? {}
    if (!/^https?:\/\//i.test(href)) {
        href = 'https://' + href
    }
    const anchor = document.createElement('a')
    anchor.href = href
    anchor.target = target
    anchor.rel = 'noopener'
    anchor.click()
}

/**
 * @function utils_options_to_dict
 * @description 将选项配置转换为字典表工具函数
 * @param options {array} 选项配置
 * @param [fileNames] {{ label: string, value: string }}
 * @return Map
 * */
export const utils_options_to_dict = (options, fileNames) => {
    const { label = 'label', value = 'value' } = fileNames ?? {}

    // 合约类型字典表
    const dict = new Map()
    options?.forEach(e => {
        dict.set(e[value], e[label])
    })

    return dict
}

/**
 * @function utils_digit_precision
 * @description 获取数字小数位数
 * @param digit {number} 选项配置
 * @return number
 * */
export const utils_digit_precision = (digit) => {
    // 整数默认保留2位
    if (_.isInteger(digit)) return 2

    const [ number, precision ] = `${digit}`.split('.')
    return precision ? precision.length : 2
}

/**
 * @function utils_big_amount_unit
 * @description 处理大金额单位转换工具函数
 * @param amount {number} 需要处理得加呢
 * @param [option] {{ type?: string, precision?: number }} 配置项
 * @param option.type {string} 处理后的数据返回类型 split 返回对象，否则返回字符串
 * @param option.precision {number} 保留小数位 默认2
 * @return {{ value: number, unit: string } | string}
 * */
export const utils_big_amount_unit = (amount, option) => {
    const { type } = option ?? {}

    if (_.isNaN(+amount)) return type === 'split' ? { value: 0, unit: '' } : '--'

    let value, unit

    const precision = utils_digit_precision(amount)

    // 取金额绝对值
    const _amount = Math.abs(amount)

    // 中文
    if (i18n.global.locale.value.slice(0, 2) === 'zh') {
        if (_amount < 10000) {
            value = _amount
            unit = ''
        } else if (_amount < 1000000) {
            value = (_amount / 10000).toFixed(precision)
            unit = '万'
        } else if (_amount < 10000000) {
            value = (_amount / 1000000).toFixed(precision)
            unit = '百万'
        } else if (_amount < 100000000) {
            value = (_amount / 10000000).toFixed(precision)
            unit = '千万'
        } else if (_amount < 100000000000) {
            value = (_amount / 100000000).toFixed(precision)
            unit = '亿'
        } else {
            value = (_amount / 100000000000).toFixed(precision)
            unit = '千亿'
        }
    } else {
        if (_amount < 1000) {
            value = _amount
            unit = ''
        } else if (_amount < 1000000) {
            value = (_amount / 1000).toFixed(precision)
            unit = 'K'
        } else if (_amount < 1000000000) {
            value = (_amount / 1000000).toFixed(precision)
            unit = 'M'
        } else if (_amount < 1000000000000) {
            value = (_amount / 1000000000).toFixed(precision)
            unit = 'B'
        } else if (_amount < 1000000000000000) {
            value = (_amount / 1000000000000).toFixed(precision)
            unit = 'T'
        } else {
            value = (_amount / 1000000000000000).toFixed(precision)
            unit = 'Q'
        }
    }

    // 恢复金额正负
    value = amount < 0 ? -value : value

    // 分割模式返回对象，否则返回字符串
    return type === 'split' ? { value, unit } : `${value}${unit}`
}

/**
 * @function utils_get_os
 * @description 获取设备系统
 * */
export const utils_get_os = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera

    const isAndroid = /android/i.test(userAgent)
    const isIOS = /iPad|iPhone|iPod/.test(userAgent)
        || (navigator.userAgent.includes('Macintosh') && 'ontouchend' in document) // iPadOS 13+

    return {
        isAndroid,
        isIOS,
    }
}

/**
 * @function utils_speech_synthesis
 * @description 语音消息
 * @param tip {string} 消息文本内容
 * */
export const utils_speech_synthesis = (tip) => {
    const synth = window.speechSynthesis
    const utterance = new SpeechSynthesisUtterance(tip)

    synth.speak(utterance)
}

/**
 * @function utils_jump
 * @description 跳转类工具函数
 * @param type {number} 跳转类型
 * @param url {string} 需要跳转的地址
 * */
export const utils_jump = (type, url) => {
    // 内部路由跳转
    if (+type === 1) {
        const path = JUMP_DICT.get(url)

        if (path) router.push(path)
    } else {
        // 跳转外部链接
        utils_link(url)
    }
}

// 解密私钥
const privateKey = CryptoJS.enc.Utf8.parse('8JUOEEGjDsmrl30P')

/**
 * @function utils_encrypt
 * @description 接口加密工具函数
 * @param content {object} 需要加密内容
 * @return object
 * */
export const utils_encrypt = (content) => {
    // 解密
    const decrypted = CryptoJS.AES.encrypt(
        JSON.stringify(content),
        privateKey,
        {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        },
    )

    // 返回UTF-8字符
    return decrypted.toString(CryptoJS.enc.Utf8)
}

/**
 * @function utils_decrypt
 * @description 接口解密工具函数
 * @param ciphertext {string} 加密文本
 * @return object
 * */
export const utils_decrypt = (ciphertext) => {
    if (typeof ciphertext !== 'string' || !ciphertext) return ciphertext

    // 解密
    const decrypted = CryptoJS.AES.decrypt(
        ciphertext,
        privateKey,
        {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        },
    )

    // 返回UTF-8字符串
    return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))
}
