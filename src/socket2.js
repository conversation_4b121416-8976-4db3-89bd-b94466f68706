class WebSocketManager {
    constructor(url) {
        this.url = url
        this.reconnectInterval = 3000
        this.heartbeatInterval = 10000
        this.maxReconnectAttempts = 5

        this.socket = null
        this.reconnectAttempts = 0
        this.heartbeatTimer = null
        this.eventHandlers = {}           // {event: [handlers]} 事件类型及其处理函数的映射
        this.subscriptions = {}          // {event: subscriptionPayload} 事件类型及其订阅数据的映射
        this.pendingMessages = []        // 存储待发送的消息
        this.connect()
    }

    // 连接到 WebSocket 服务器
    connect() {
        // 如果 socket 已经存在且连接中或者已经连接，则不再重复连接
        if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
            console.warn('WebSocket 已经连接或者正在连接中.')  // 提示警告
            return
        }

        // 返回一个 Promise 对象，表示异步连接 WebSocket
        return new Promise((resolve, reject) => {
            // 创建 WebSocket 连接
            this.socket = new WebSocket(this.url)

            // 当 WebSocket 连接成功时执行的回调函数
            this.socket.onopen = async () => {
                console.log('WebSocket 连接成功.')
                // 连接成功后重置重连次数
                this.reconnectAttempts = 0

                // 启动心跳机制（定时发送 ping 消息，保持连接）
                this.startHeartbeat()

                // 执行待处理的消息（如果有）
                this.flushPendingMessages()

                // 自动重新订阅之前的事件
                this.resubscribeEvents()

                // 连接成功后调用 resolve，表示 Promise 完成
                resolve()
            }

            // 当收到 WebSocket 消息时的回调
            this.socket.onmessage = (event) => {
                // 如果收到的数据不是 'pong'，则进行事件处理
                if (event.data !== 'pong') {
                    const parse = JSON.parse(event.data)  // 解析 JSON 数据
                    // 如果数据中包含 'type' 字段，触发相应的事件
                    if ('type' in parse) {
                        this.triggerEvent(parse.type, parse)
                    }
                    // 如果数据中包含 'action' 字段，触发相应的事件
                    // if ('action' in parse) {
                    //     this.triggerEvent(parse.action, parse)
                    // }
                }
            }

            // 当 WebSocket 连接关闭时的回调
            this.socket.onclose = () => {
                console.warn('WebSocket 连接关闭.')  // 提示警告
                this.stopHeartbeat()  // 停止心跳机制
                this.tryReconnect()   // 尝试重新连接
                reject(new Error('WebSocket 连接关闭.'))  // 返回一个错误，表示连接关闭
            }

            // 当 WebSocket 发生错误时的回调
            this.socket.onerror = (error) => {
                console.error('WebSocket 错误:', error)  // 打印错误信息
                reject(error)  // 返回一个错误，表示连接出错
            }
        })
    }


    // 尝试重新连接
    tryReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('已达到最大重连次数.')
            return
        }

        this.reconnectAttempts += 1
        console.log(`正在重新连接... 第 ${this.reconnectAttempts} 次尝试`)
        setTimeout(() => {
            this.connect().catch((error) => console.error('重连失败:', error))
        }, this.reconnectInterval)
    }

    // 心跳机制
    startHeartbeat() {
        if (this.heartbeatTimer) return

        this.heartbeatTimer = setInterval(() => {
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send('ping')
            }
        }, this.heartbeatInterval)
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
        }
    }

    // 发送消息
    async emit(data) {
        await this.waitForConnection()
        if (this.socket) {
            this.socket.send(JSON.stringify(data))
        }
    }

    // 等待 WebSocket 连接准备好
    async waitForConnection() {
        return new Promise((resolve) => {
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                resolve()
            } else {
                this.pendingMessages.push(resolve)
            }
        })
    }

    // 刷新待发送的消息
    flushPendingMessages() {
        while (this.pendingMessages.length > 0) {
            const resolve = this.pendingMessages.shift()
            resolve()
        }
    }

    // 关闭连接
    close() {
        this.stopHeartbeat()
        if (this.socket) {
            this.socket.close()
            this.socket = null
        }
    }

    on(event, handler, subscriptionPayload = null) {
        const key = `${event}${subscriptionPayload.params.instrument}`
        if (!this.eventHandlers[key]) {
            this.eventHandlers[key] = []
        }

        this.eventHandlers[key] = [ handler ]

        // 保存订阅信息以便在重连时使用
        if (subscriptionPayload) {
            this.subscriptions[key] = subscriptionPayload
            this.emit(subscriptionPayload)
        }
    }

    // 移除指定的事件处理函数
    off(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event] = this.eventHandlers[event].filter((h) => h !== handler)
        }
    }

    // 触发事件的所有处理函数
    triggerEvent(event, res) {
        const { type, data } = res
        const _event = (type === 'market' && data) ? `market${data.detail.market}|${data.detail.securityType}|${data.detail.symbol}` : event
        if (this.eventHandlers[_event]) {
            this.eventHandlers[_event].forEach((handler) => handler(res))
        }
    }

    // 重新订阅所有事件并附带保存的订阅数据
    resubscribeEvents() {
        for (const event in this.subscriptions) {
            const payload = this.subscriptions[event]
            this.emit(payload)
        }
    }
}

// 默认导出 WebSocketManager 的实例
export default new WebSocketManager(`${window.location.protocol === 'https:' ? 'wss' : 'ws'}://${window.location.host}/ws`)
