<template>
    <van-dialog
        v-model:show="isShow"
        title=""
        :show-cancel-button="false"
        :show-confirm-button="false"
        teleport="body"
    >
       <div class="py-5 px-3 text-primary text-lg">
           <p class="flex justify-between">
               <span>{{t("financial._financial")}}</span>
               <van-icon @click="emit('close')" size="24" name="cross" />
           </p>
           <div class="flex justify-between mt-5">
               <div
                   class="flex flex-col justify-center items-center bg-page w-[48.5%] h-[80px] rounded-lg"
                   v-for="({text, icon, to, id}) in nav"
                   @click="handleJump(to)"
                   :key="id"
               >
                   <c-icon size="24"  prefix="account" :name="icon" />
                   <span class="text-base mt-2.5">{{ text }}</span>
               </div>
           </div>
       </div>
    </van-dialog>
</template>

<script setup>
import {useRouter} from 'vue-router'

const router = useRouter()
const isShow = defineModel( {
    type: Boolean,
    default: false
})

const emit = defineEmits(['close'])

const {t} = useI18n()

const handleJump = (to) => {
    if (to === '/deposit') {
        sessionStorage.setItem('depositActiveTab', 'bank')
        router.push('/deposit')
    } else {
        router.push(to)
    }
}

const nav = computed(() => {
    return  [
        {
            id: 'x001',
            text:  t('account.deposit'),
            icon: 'grid_deposit',
            to: '/deposit',
        },
        {
            id: 'x002',
            text:  t('account.withdrawal'),
            icon: 'grid_withdrawal',
            to: '/withdrawal',
        }
    ]
})

defineOptions({
    title: 'FunTransfers',
})
</script>