<template>
    <div class="c-search flex-middle gap-4 bg-transparent">
        <div class="c-search__input flex-1 h-8 flex-middle bg-bg rounded-full px-2.5 gap-1.5 text-text">
            <van-icon name="search" size="14"/>

            <input
                class="flex-1 w-1 text-xs! bg-transparent text-input"
                :placeholder="placeholder ?? t('_placeholder')"
                :readonly
                :value="modelValue"
                @input="onInput"
                @compositionstart="isComposing = true"
                @compositionend="onCompositionEnd"
            />

            <van-icon
                v-if="modelValue"
                name="clear"
                @click="modelValue = ''"
            />
        </div>

        <van-button
            v-if="searchBtn"
            size="small"
            type="primary"
            :loading
            :disabled
            @click="emits('search')"
        >
            <slot>
                {{ t('_search') }}
            </slot>
        </van-button>
    </div>
</template>

<script setup>
import _ from 'lodash'

const isComposing = ref(false)
const { searchBtn } = defineProps({
    readonly: <PERSON><PERSON><PERSON>,
    placeholder: String,
    searchBtn: <PERSON><PERSON>an,
    loading: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
})

const onCompositionEnd = (e) => {
    isComposing.value = false
    onInput(e) // 确保输入结束后触发一次 input 逻辑
}

const modelValue = defineModel({
    type: [ String, Number ],
})

const emits = defineEmits([ 'search' ])

const onSearch = _.debounce(
    value => {
        emits('search', value)
    },
    300,
)

const onInput = ({ target: { value } }) => {
    if (isComposing.value) return // 拼音输入未完成，跳过
    modelValue.value = value

    if (!searchBtn) onSearch(value)
}

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _placeholder: '搜索股票/代码',
            _search: '搜索',
        },
        [LANGUAGE.zhHK]: {
            _placeholder: '檢索股票/代碼',
            _search: '搜索',
        },
        [LANGUAGE.enUS]: {
            _placeholder: 'Search stock/code',
            _search: 'Search',
        },
    },
})

defineOptions({ name: 'C-Search' })
</script>

<style scoped>

</style>
