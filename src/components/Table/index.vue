<template>
    <div class="c-table text-sm px-2.5 rounded-lg bg-card custom-shadow relative">
        <!-- Header -->
        <div
            data-aos="fade-left"
            data-aos-delay="50"
            data-aos-anchor="#app"
            class="c-table__header text-title grid h-10 border-b border-border"
            :style="{ gridTemplateColumns }"
        >
            <div
                class="h-full flex-middle gap-2"
                v-for="({ title, dataIndex, titleClass, sortable, align }, index) in columns"
                :key="dataIndex ?? index"
                :class="_.concat(titleClass, {
                    'justify-center': align === 'center',
                    'justify-end': align === 'right',
                    'opacity-70': refreshLoading
                })"
                @click="onSort({ dataIndex, index, sortable })"
            >
                <component :is="typeof title === 'function' ? title : () => title"/>

                <template v-if="sortable">
                    <div class="flex-center flex-col">
                        <c-icon
                            name="arrow_triangle"
                            size="8"
                            class="rotate-180"
                            :class="[ sortCircle[sortStatus[index]] === SORT_CONFIG.ASCENDING ? 'text-active' : 'text-text' ]"
                        />
                        <c-icon
                            name="arrow_triangle"
                            size="8"
                            :class="[ sortCircle[sortStatus[index]] === SORT_CONFIG.DESCENDING ? 'text-active' : 'text-text' ]"
                        />
                    </div>
                </template>
            </div>
        </div>
        <!-- Header -->

        <!-- Body -->
        <div class="c-table__body-container overflow-auto">
            <div class="h-full">
                <c-record
                    :finished
                    :onRefresh="_onRefresh"
                    :onLoadMore
                    :class="{ 'h-full': !dataSource?.length }"
                    :list-props="{
                        class: { 'h-full flex-center flex-col': !dataSource?.length }
                    }"
                    :disabled
                    v-model:refresh-loading="refreshLoading"
                    v-model:load-loading="loadLoading"
                >
                    <div
                        data-aos="fade-left"
                        data-aos-anchor="#app"
                        class="c-table__body grid gap-2 min-h-8 py-2 items-center"
                        v-for="(data, index) in dataSource"
                        :data-aos-delay="index * 50"
                        :key="index"
                        :style="{ gridTemplateColumns }"
                        :class="_.concat(_.isFunction(rowsClass) ? rowsClass(data, index) : rowsClass)"
                        @click="emits('row-click', data, index)"
                    >
                        <ContentRender :data :index/>
                    </div>
                </c-record>
            </div>
        </div>
        <!-- Body -->
    </div>
</template>

<script setup>
import _ from 'lodash'

import { SORT_CONFIG } from '@/config'

const { columns, dataSource, onRefresh, initialSortStatus } = defineProps({
    columns: {
        type: Array,
        required: true,
    },
    dataSource: {
        type: Array,
        required: true,
    },
    finished: Boolean,
    headerClass: [ String, Array, Object ],
    rowsClass: [ String, Array, Object, Function ],
    onRefresh: Function,
    onLoadMore: Function,
    disabled: Boolean,
    initialSortStatus: {
        type: Array,
        default: () => [], // 默认空数组
    },
})

const refreshLoading = defineModel('refreshLoading', Boolean),
    loadLoading = defineModel('loadLoading', Boolean)

const emits = defineEmits([ 'filter', 'row-click' ])

// 表格分栏
const gridTemplateColumns = computed(() => columns.map(e => e?.width ?? '1fr').join(' '))

// 排序
const sortDefault = columns.map(() => 0),
    sortCircle = [
        SORT_CONFIG.NORMAL,
        SORT_CONFIG.ASCENDING,
        SORT_CONFIG.DESCENDING,
    ]

// 初始化排序状态，如果父组件传了 initialSortStatus 就用它
const sortStatus = ref(
    initialSortStatus.length ? initialSortStatus.slice() : sortDefault,
)

const onSort = ({ dataIndex, index, sortable }) => {
    if (!refreshLoading.value && sortable) {
        const newSort = [ ...sortDefault ]
        newSort[index] = (sortStatus.value[index] + 1) % sortCircle.length
        sortStatus.value = newSort

        emits('filter', { dataIndex, index, sortable, sort: sortCircle[sortStatus.value[index]] })
    }
}

const _onRefresh = () => {
    sortStatus.value = [ ...sortDefault ]
    onRefresh()
}

const ContentRender = ({ data, index }) => {
    return columns.map(column => {
        const { dataIndex, render, align, class: className } = column

        const child = render ? render(dataIndex ? data[dataIndex] : data, data, index) : data[dataIndex]

        if (_.isString(child)) {
            return h(
                'span',
                {
                    class: [
                        'truncate',
                        {
                            'text-center': align === 'center',
                            'text-end': align === 'right',
                        },
                    ],
                },
                child,
            )
        }

        return h(
            'div',
            {
                class: [
                    className,
                    'w-full flex-middle overflow-hidden',
                    {
                        'justify-center': align === 'center',
                        'justify-end': align === 'right',
                    },
                ],
            },
            child,
        )
    })
}

defineOptions({ name: 'C-Table' })
</script>

<style scoped>
.c-table__body-container {
    /*
        Header 48px
    */
    height: calc(100% - 48px);
}
</style>
