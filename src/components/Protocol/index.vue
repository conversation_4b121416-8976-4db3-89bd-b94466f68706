<template>
    <van-popup
        round
        position="bottom"
        teleport="body"
        class="flex flex-col"
        style="min-height: 50vh; max-height: 80vh;"
        v-model:show="modelValue"
    >
        <div class="text-title leading-10 flex-between px-5 font-semibold text-md">
            <span></span>
            {{ title }}
            <van-icon @click="modelValue = false" name="cross"/>
        </div>

        <div
            class="flex-1 p-4 overflow-auto text-paragraph"
            v-html="content"
        />
    </van-popup>
</template>

<script setup>
defineProps({
    title: {
        type: String,
        required: true,
    },
    content: {
        type: String,
        required: true,
    },
})

const modelValue = defineModel({
    type: Boolean,
    required: true,
})

defineOptions({ name: 'C-Protocol' })
</script>

<style scoped>

</style>
