<!--<template>-->
<!--    <div-->
<!--        class="flex flex-col items-center justify-center"-->
<!--        v-bind="$attrs"-->
<!--    >-->
<!--        <span class="w-10 h-10 flex items-center justify-center bg-bg1 rounded-[50%]">-->
<!--            <c-icon :name="icon"/>-->
<!--        </span>-->
<!--        <slot/>-->
<!--    </div>-->
<!--</template>-->

<!--<script setup>-->
<!--const { icon, alt, width, height, theme, async } = defineProps({-->
<!--    icon: {-->
<!--        type: [ String, Object ],-->
<!--        required: true,-->
<!--    },-->
<!--    width: Number,-->
<!--    height: Number,-->
<!--    alt: String,-->
<!--    theme: Boolean,-->
<!--    async: Boole<PERSON>,-->
<!--})-->
<!--const bgImage = `/skins/templates/${G_TEMPLATE}/${G_THEME}/bg_vip.png`-->

<!--const backgroundStyle = computed(() => {-->
<!--    return {-->
<!--        background: `url(${bgImage})`,-->
<!--        backgroundRepeat: 'no-repeat',-->
<!--        backgroundSize: 'auto 100%',-->
<!--    }-->
<!--})-->

<!--defineOptions({ name: 'C-Image-Box-Shield', inheritAttrs: false })-->
<!--</script>-->

<template>
    <div
        class="c-image-box relative"
        :class="containerClass"
        v-bind="containerAttributes"
    >
        <Image/>

        <div class="c-image-box__container absolute w-full h-full left-0 top-0" v-bind="$attrs">
            <slot/>
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash'

import AsyncImage from '@/components/AsyncImage/index.vue'

const uid = useId()

const { source, alt, width, height, theme, async } = defineProps({
    source: {
        type: [ String, Object ],
        required: true,
    },
    width: Number,
    height: Number,
    containerClass: [ String, Array, Object ],
    alt: String,
    theme: Boolean,
    async: Boolean,
})

const containerAttributes = _.pickBy(useAttrs(), (val, key) => _.startsWith(key, 'data-'))

const _alt = alt ?? `imageBox-${uid}`

const Image = () => {
    const style = {
        width: `${width}px`,
        height: `${height}px`,
    }

    if (async) {
        return h(
            AsyncImage,
            {
                name: source,
                alt: _alt,
                theme,
                style,
            },
        )
    } else {
        return h(
            'img',
            {
                src: source,
                alt: _alt,
                style,
            },
        )
    }
}

defineOptions({ name: 'C-Image-Box', inheritAttrs: false })
</script>

<style scoped>
</style>
