<template>
    <van-floating-bubble
        v-if="bubbleVisible"
        icon="chat"
        axis="xy"
        magnetic="x"
        :gap="{ x: 20, y: 80 }"
        @click="utils_link($globalConfig?.service)"
    >

    </van-floating-bubble>
</template>

<script setup>
import { utils_link } from '@/utils/index.js'

const { currentRoute } = useRouter()

const { $globalConfig } = storeToRefs(useGlobalStore()),
    { $isLogin } = storeToRefs(useProfileStore()),
    bubbleVisible = computed(() => $isLogin.value && currentRoute.value.name !== 'service' && $globalConfig.value.service)


</script>
