<template>
    <Controller
        v-bind="_.pick(attrs, [ 'class', 'required', 'disabled' ])"
    >
        <template v-if="$slots.prefix" #prefix>
            <slot name="prefix"/>
        </template>

        <input
            ref="inputRef"
            class="h-full flex-1 w-1 bg-transparent"
            :class="[ attrs?.disabled ? 'text-input' : 'text-title' ]"
            v-bind="_.omit(attrs, 'class')"
            :type="htmlType"
            v-model="modelValue"
            :min="attrs.min"
            :max="attrs.max"
            @input="handleInput"
            @focus="focused = true"
        />

        <template v-if="clearable">
            <c-icon
                v-show="modelValue && focused"
                prefix="auth"
                name="clear"
                @click="modelValue = ''"
            />
        </template>

        <template v-if="attrs.type === 'password'">
            <c-icon
                prefix="auth"
                :name="isPassword ? 'eye_open' : 'eye_close'"
                @click="htmlType = isPassword ? 'text' : 'password'"
            />
        </template>

        <template v-if="$slots.suffix" #suffix>
            <slot name="suffix"/>
        </template>

        <template v-if="$slots.addOnBefore" #addOnBefore>
            <slot name="addOnBefore"/>
        </template>

        <template v-if="$slots.addOnAfter" #addOnAfter>
            <slot name="addOnAfter"/>
        </template>
    </Controller>
</template>

<script setup>
import _ from 'lodash'

import Controller from './Controller.vue'

defineProps({
    clearable: {
        type: Boolean,
        default: true,
    },
})

const attrs = useAttrs()

const modelValue = defineModel({
    required: false,
    type: [ String, Number ],
})

const htmlType = ref(attrs?.type ?? 'text'),
    isPassword = computed(() => htmlType.value === 'password'),
    focused = ref(false)

const domRef = useTemplateRef('inputRef')
onClickOutside(domRef, () => {
    focused.value = false
})

const handleInput = (e) => {
    const inputValue = e.target.value

    // 只在 number 类型下处理 min/max 限制
    if (htmlType.value === 'number') {
        const val = Number(inputValue)
        if (isNaN(val)) return

        const min = attrs.min != null ? Number(attrs.min) : null
        const max = attrs.max != null ? Number(attrs.max) : null

        if (min != null && val < min) {
            modelValue.value = min
        } else if (max != null && val > max) {
            modelValue.value = max
        } else {
            modelValue.value = val
        }
    } else {
        // 非 number 类型保持原样（例如 text/password）
        modelValue.value = inputValue
    }
}


defineOptions({ name: 'C-Input' })
</script>

<style scoped>

</style>
