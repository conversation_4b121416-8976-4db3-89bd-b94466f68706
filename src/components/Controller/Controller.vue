<template>
    <div
        class="c-controller"
        ref="domRef"
        :class="[ disabled ? 'text-text' : 'text-title' ]"
    >
        <slot name="left">
            <div v-if="label" class="mb-3 text-title">
                <span>{{ label }}</span>

                <sup v-if="required" class="text-red ml-1">*</sup>
            </div>
        </slot>

        <div class="flex items-center gap-2.5 text-sm">
            <slot name="addOnBefore"/>

            <div class="c-controller__main min-h-10 rounded-lg bg-controller_bg flex-between px-3 gap-2 flex-1 w-1">
                <div v-if="$slots.prefix" class="c-controller__prefix">
                    <slot name="prefix"/>
                </div>

                <slot :width="domRef?.clientWidth"/>

                <div v-if="$slots.suffix" class="c-controller__suffix h-5">
                    <slot name="suffix"/>
                </div>
            </div>

            <slot name="addOnAfter"/>
        </div>
    </div>
</template>

<script setup>
defineProps({
    label: String,
    required: <PERSON><PERSON>an,
    disabled: Boolean,
})

const domRef = useTemplateRef('domRef')

defineOptions({ name: 'C-Controller' })
</script>

<style scoped>
</style>
