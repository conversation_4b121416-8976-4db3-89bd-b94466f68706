<template>
    <div class="skew-tabs-container px-2.5 py-1" :style="{ '--width': width, '--height': height }">
        <div class="skew-tabs-wrapper">
            <button
                v-for="(tab, idx) in tabs"
                :key="tab.key"
                class="skew-tab-button"
                :class="[
                    modelValue === tab.value ? 'active' : 'inactive',
                    isTempCoin && (idx % 2 === 1) ? 'reverse-skew-btn' : 'skew-btn'
                ]"
                :style="getButtonStyle(tab, idx)"
                @click="handleTabClick(tab)"
            >
                {{ tab.label }}
            </button>
        </div>
    </div>
</template>

<script setup>

const props = defineProps({
    // Current active tab value
    modelValue: {
        type: [String, Number],
        required: true
    },
    // Array of tab objects with label, value, key properties
    tabs: {
        type: Array,
        required: true,
        default: () => []
    },
    // Whether to use temp_coin template styling
    isTempCoin: {
        type: Boolean,
        default: false
    },
    // Custom active background color
    activeBgColor: {
        type: String,
        default: 'var(--primary)'
    },
    // Custom inactive background color
    inactiveBgColor: {
        type: String,
        default: 'var(--bg-2)'
    },
    // Custom active text color
    activeTextColor: {
        type: String,
        default: 'white'
    },
    // Custom inactive text color
    inactiveTextColor: {
        type: String,
        default: 'var(--primary)'
    },
    // Custom width
    width: {
        type: String,
        default: '100%'
    },
    // Custom height
    height: {
        type: String,
        default: '40px'
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const handleTabClick = (tab) => {
    emit('update:modelValue', tab.value)
    emit('change', tab)
}

const getButtonStyle = (tab, idx) => {
    const isActive = props.modelValue === tab.value
    const bgColor = isActive ? props.activeBgColor : props.inactiveBgColor
    const textColor = isActive ? props.activeTextColor : props.inactiveTextColor
    
    return {
        '--skew-bg-color': bgColor,
        color: textColor
    }
}
</script>

<style scoped>
.skew-tabs-container {
    width: var(--width);
    border-radius: 8px;
    background: var(--bg);
}

.skew-tabs-wrapper {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    padding: 0 10px;
}

.skew-tab-button {
    position: relative;
    flex: 1;
    height: var(--height);
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    outline: none;
    &:nth-child(odd) {
        margin-left: 10px;
    }
    &:nth-child(even) {
        margin-right: 15px;
    }
    &:first-child {
        margin-left: 0;
    }
    &:last-child {
        margin-right: 0;
    }
}

.skew-tab-button {
    transform: translateY(-1px);
}

.skew-tab-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
/* Responsive design */
@media (max-width: 480px) {
    .skew-tabs-wrapper {
        gap: 5px;
        padding: 0 5px;
    }
    
    .skew-tab-button {
        height: 36px;
        font-size: 12px;
    }
}
</style> 