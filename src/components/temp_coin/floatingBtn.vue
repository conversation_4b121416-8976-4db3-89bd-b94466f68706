<template>
    <van-floating-bubble
        class="w-[56px] h-[56px]"
        v-if="bubbleVisible"
        icon="chat"
        axis="xy"
        magnetic="x"
        :gap="{ x: 20, y: 80 }"
        @click="utils_link($globalConfig?.service)"
    >
        <div class="flex flex-col items-center justify-center">
            <c-icon name="customer01" size="20"/>

            <div class="text-[10px] text-white">
                {{ $t('header.service') }}
            </div>
        </div>

    </van-floating-bubble>
</template>

<script setup>
import { utils_link } from '@/utils/index.js'

const { currentRoute } = useRouter()

const { $globalConfig } = storeToRefs(useGlobalStore()),
    { $isLogin } = storeToRefs(useProfileStore()),
    bubbleVisible = computed(() => $isLogin.value && currentRoute.value.name !== 'service' && $globalConfig.value.service)

defineOptions({
    name: 'FloatingBtn',
})
</script>
