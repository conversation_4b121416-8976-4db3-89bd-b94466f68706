<template>
    <div class="c-amount inline-flex" :class="[color, _color ]">
        <!-- Prefix -->
        <slot name="prefix">
            <span v-if="prefix">{{ prefix }}</span>
        </slot>
        <!-- Prefix -->

        <!-- CountTo -->
        <CountTo
            v-if="animate"
            :prefix="symbol ? _symbol : ''"
            :count="+_amount.value"
            :precision
            v-bind="countProps"
        />
        <!-- CountTo -->

        <!-- 普通金额展示 -->
        <span class="whitespace-nowrap" v-else>
            <template v-if="symbol">{{ _symbol }}</template>
            <span :class="fontSize">
                {{ _amount.value }}
            </span>
        </span>
        <!-- 普通金额展示 -->

        <!-- 大金额单位 -->
        <span v-if="_amount.unit">
            {{ _amount.unit }}
        </span>
        <!-- 大金额单位 -->

        <!-- 货币符号 -->
        <span v-if="currency">
            {{ currency }}
        </span>
        <!-- 货币符号 -->

        <!-- Suffix -->
        <slot name="suffix">
            <span v-if="percent">%</span>
            <span v-if="suffix">{{ suffix }}</span>
        </slot>
        <!-- Suffix -->
    </div>
</template>

<script setup>
import currencyJs from 'currency.js'

import CountTo from '../CountTo/index.vue'

const {
    fontSize = 'text-xs',
    amount,
    symbol,
    animate,
    currencyConfig,
    precision,
    color,
    colorful,
    bigUnit,
    currency,
    currencyRate,
} = defineProps({
    // 金额
    amount: {
        required: true,
    },
    // 正负符号
    symbol: Boolean,
    // 货币标识
    currency: String,
    // 货币汇率换算开关
    currencyRate: Boolean,
    // 数字动画
    animate: {
        type: Boolean,
        default: true,
    },
    // 保留小数位数
    precision: {
        type: Number,
        default: 2,
    },
    // CountTo 组件参数
    countProps: Object,
    // 正负数红绿颜色开关
    color: String,
    // 是否开启正负颜色显示
    colorful: Boolean,
    prefix: String,
    suffix: String,
    // 百分比符号
    percent: Boolean,
    // 大额数字缩写
    bigUnit: Boolean,
    // currency.js 配置
    currencyConfig: {
        type: Object,
        default: {
            symbol: '',
        },
    },
    fontSize: String,
})

const { dispatch_getCurrencyRateConfig } = useRateStore()

// 取传递颜色或者正负颜色
const _color = computed(() => color || (colorful ? utils_amount_color(amount) : undefined)),
    // 正数符号
    _symbol = computed(() => amount > 0 ? '+' : ''),
    // 金额格式化
    _amount = computed(() => {
        let value, unit, rateAmount = amount

        // 有币种且需要与对应货币汇率进行换算
        if (currency && currencyRate) {
            const { rate } = dispatch_getCurrencyRateConfig(currency)
            rateAmount = currencyJs(rateAmount).multiply(rate)
        }

        if (!bigUnit) {
            value = rateAmount
        } else {
            // 大金额格式化
            ({ value, unit } = utils_big_amount_unit(rateAmount, { type: 'split' }))
        }

        // 不开 CountTo 动画显示 需要币种格式化
        if (!animate) {
            value = utils_currency(value, { ...currencyConfig, precision })
        }

        return {
            value,
            unit,
        }
    })

// 金额展示组件
defineOptions({ name: 'C-Amount' })
</script>

<style scoped>
.c-amount {
    font-family: 'digit', sans-serif;
}
</style>
