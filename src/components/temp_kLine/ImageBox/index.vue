<template>
    <div
        class="c-image-box relative"
        :class="containerClass"
        v-bind="containerAttributes"
    >
        <Image/>

        <div class="c-image-box__container absolute w-full h-full left-0 top-0" v-bind="$attrs">
            <slot/>
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash'

import AsyncImage from '@/components/AsyncImage/index.vue'

const uid = useId()

const { source, alt, width, height, theme, async } = defineProps({
    source: {
        type: [ String, Object ],
        required: true,
    },
    width: Number,
    height: Number,
    containerClass: [ String, Array, Object ],
    alt: String,
    theme: Boolean,
    async: Boolean,
})

const containerAttributes = _.pickBy(useAttrs(), (val, key) => _.startsWith(key, 'data-'))

const _alt = alt ?? `imageBox-${uid}`

const Image = () => {
    const style = {
        width: `${width}px`,
        height: `${height}px`,
    }

    if (async) {
        return h(
            AsyncImage,
            {
                name: source,
                alt: _alt,
                theme,
                style,
            },
        )
    } else {
        return h(
            'img',
            {
                src: source,
                alt: _alt,
                style,
            },
        )
    }
}

defineOptions({ name: 'C-Image-Box', inheritAttrs: false })
</script>

<style scoped>
</style>
