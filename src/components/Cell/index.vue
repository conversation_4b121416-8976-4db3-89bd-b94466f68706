<template>
    <div
        data-aos="fade-left"
        data-aos-anchor="#app"
        class="c-cell custom-shadow h-16 px-4 flex-middle gap-2 not-last:mb-2.5"
        @click="onClick"
    >
        <slot name="icon">
            <c-icon
                v-if="icon"
                v-bind="icon"
            />
        </slot>

        <div class="flex-1 w-1">
            <slot name="title">
                <div v-if="title" class="text-title text-sm">
                    {{ title }}
                </div>
            </slot>
            <slot name="subhead">
                <div v-if="subhead" class="text-text text-xs mt-1.5">
                    {{ subhead }}
                </div>
            </slot>
        </div>

        <slot name="extra">
            {{ extra }}
        </slot>

        <van-icon
            v-if="arrow || to"
            name="arrow"
            color="#F2F2F2"
        />
    </div>
</template>

<script setup>
const { push } = useRouter()

const { to } = defineProps({
    icon: Object,
    title: String,
    subhead: String,
    extra: String,
    arrow: <PERSON>olean,
    to: [ String, Object ],
})

const emits = defineEmits([ 'click' ])

const onClick = () => {
    if (to) push(to)

    emits('click')
}

defineOptions({ name: 'C-Cell' })
</script>

<style scoped>
</style>
