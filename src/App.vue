<template>
    <van-config-provider
        class="h-full"
        :theme="$theme"
    >
        <router-view/>
        <FloatingBtn/>
    </van-config-provider>
</template>

<script setup>
import { $theme } from '@/store'
import socket from '@/socket.js'
import FloatingBtn from '@/components/_TEMPLATE_/floatingBtn.vue'

const { currentRoute } = useRouter()

useMarketStatusStore()

const messageStore = useMessageStore()
const { dispatch_refreshUnreadCount } = messageStore

const { $globalConfig } = storeToRefs(useGlobalStore()),
    { $isLogin } = storeToRefs(useProfileStore()),
    bubbleVisible = computed(() => $isLogin.value && currentRoute.value.name !== 'service' && $globalConfig.value.service)

const { $inviteCode } = storeToRefs(useGlobalStore())
const urlParams = new URLSearchParams(window.location.hash.split('?')[1]),
    inviteCode = urlParams.get('inviteCode')

if (inviteCode) $inviteCode.value = inviteCode

document.documentElement.dataset.theme = $theme.value

let warningNotifyInstance

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _warning: '股票提醒',
        },
        [LANGUAGE.zhHK]: {
            _warning: '股票提醒',
        },
        [LANGUAGE.enUS]: {
            _warning: 'Stock Alert',
        },
    },
})

const warningToast = ({ data }) => {
    utils_speech_synthesis(t('_warning'))
    warningNotifyInstance = showNotify({
        type: 'warning',
        message: data.content,
        onClick: () => {
            warningNotifyInstance.close()
        },
    })
}
// 股票预警
socket.on(SOCKET_EVENTS.WARNING, warningToast)
// 强平
socket.on(SOCKET_EVENTS.CLOSE, warningToast)

// 系统消息
socket.on(SOCKET_EVENTS.SYSTEM, ({ data }) => {
    console.warn('系统消息')
    const systemNotifyInstance = showNotify({
        type: 'primary',
        message: data.content,
        onClick: () => {
            systemNotifyInstance.close()
        },
    })
    // 更新红点信息 如果红点信息变化了 更新消息列表
    dispatch_refreshUnreadCount()
})
</script>
