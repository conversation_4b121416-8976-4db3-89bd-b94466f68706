// 银行卡状态管理
export const useBankStore = defineStore('bank', () => {
    const { res: $bankList, loading: $bankLoading, onRefresh: dispatch_refreshBank } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E9%93%B6%E8%A1%8C%E5%8D%A1/userBankListUsingGET_1
        url: '/bank/userBank/list',
        initialValues: [],
        needLogin: true,
        cancellable: false,
        manual: true
    })

    return {
        $bankList,
        $bankLoading,
        dispatch_refreshBank,
    }
})

// 提现状态管理
export const useWithdrawalStore = defineStore('withdrawal', () => {
    const $withdrawalConfig = ref({
        handlingFeeModel: 0,
        id: 0,
        maxWithdrawalAmount: 0,
        minWithdrawalAmount: 0,
        testUsersCanWithdraw: 0,
        withdrawalFee: 0,
        withdrawalStatus: 0,
        withdrawalsPerDay: 0,
    })

    const [ dispatch_getWithdrawalConfig ] = useFetchLoading(async () => {
        $withdrawalConfig.value = await api_get({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%8F%90%E7%8E%B0%E9%85%8D%E7%BD%AE%20-%20Withdrawal%20Configuration%20API/getWithdrawalConfigUsingGET_1
            url: '/withdrawalConfig/get',
        })
    })

    return {
        $withdrawalConfig,
        dispatch_getWithdrawalConfig,
    }
})

export const useWalletStore = defineStore('wallet', () => {
    const { res: $channel, loading: $channelLoading, onRefresh: dispatch_refreshChannel } = useRequest({
        url: API_PATH.THIRD_CHANNEL,
        params: {
            type: 2
        },
        initialValues: [],
        cancellable: false,
        needLogin: true
    })

    const { res: $wallet, loading: $walletLoading, onRefresh: dispatch_refreshWallet } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%94%A8%E6%88%B7%E9%92%B1%E5%8C%85/getUserWalletListUsingGET_1
        url: '/user_wallet/getUserWalletList',
        initialValues: [],
        cancellable: false,
        needLogin: true
    })

    // 获取可绑定的支付方式
    const { res: $allWallet, loading: $allWalletLoading, onRefresh: dispatch_refreshAllWallet } = useRequest({
        url: '/user_wallet/getCanBindPayWayList',
        initialValues: [],
        cancellable: false,
        needLogin: true
    })


    return {
        $channel,
        $channelLoading,
        dispatch_refreshChannel,

        $wallet,
        $walletLoading,
        dispatch_refreshWallet,

        $allWallet,
        $allWalletLoading,
        dispatch_refreshAllWallet,
    }
})