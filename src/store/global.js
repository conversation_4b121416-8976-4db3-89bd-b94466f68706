// 主题
export const $theme = useLocalStorage('theme', THEME_CONFIG.LIGHT)

// 涨跌红绿
export const $raise_fall_color = useLocalStorage('$raise_fall_color', RAISE_FALL_COLOR_CONFIG.RED_GREEN)

const initialValues = {
    favicon: '',
    logo_light: '',
    validProxyLevel: 0,
    title: DEFAULT_LANGUAGE,
    logo_dark: '',
    slogan: DEFAULT_LANGUAGE,
    service: '',
    chat: '',
}

// 全局配置状态管理
export const useGlobalStore = defineStore('global', () => {
    const { locale } = useI18n()

    const $inviteCode = useSessionStorage('inviteCode', '')

    const {
        res: $globalConfig,
        loading: $globalLoading,
        onRefresh: dispatch_refreshGlobalConfig,
    } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%AB%99%E7%82%B9%E9%85%8D%E7%BD%AE/getSettingsUsingGET_1
        url: '/site/sysSettings',
        initialValues: { ...initialValues },
        cancellable: false,
        formatResult: _res => _res ?? { ...initialValues },
        onSuccess: res => {
            useTitle(res.title[locale.value])
            useFavicon(res.favicon)
        },
        sessionKey: 'globalConfig',
    })

    const $logo = computed(() => {
        const { logo_light, logo_dark } = $globalConfig.value
        return $theme.value === THEME_CONFIG.LIGHT ? logo_light : logo_dark
    })

    return {
        $logo,
        $inviteCode,
        $globalConfig,
        $globalLoading,
        dispatch_refreshGlobalConfig,
    }
})

export const useNoticeStore = defineStore('notice', () => {
    const $noticePopup = ref(false)

    const {
        res: $notice,
        initial: $noticeInitial,
        loading: $noticeLoading,
        refreshLoading: $noticeRefreshLoading,
        onRefresh: dispatch_refreshNotice,
    } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%B4%BB%E5%8A%A8%E5%85%AC%E5%91%8A%E9%80%9A%E7%9F%A5%E5%A4%A7%E7%B1%BB%E7%AE%A1%E7%90%86/listUsingGET_3
        url: '/notification/list',
        initialValues: {
            activity: [],
            marquee: [],
            popup: [],
        },
        formatResult: res => {
            const activity = [],
                marquee = [],
                popup = []

            res.forEach(e => {
                switch (e.type) {
                    // 活动公告
                    case 1:
                        activity.push(e)
                        break
                    // 滚动公告
                    case 2:
                        marquee.push(e)
                        break
                    // 弹窗公告
                    case 3:
                        popup.push(e)
                        break
                }
            })

            if (popup.length && !$noticeInitial.value) {
                $noticePopup.value = true
            }

            return {
                activity,
                marquee,
                popup,
            }
        },
    })

    const toggleNoticePopup = () => {
        $noticePopup.value = !$noticePopup.value
    }

    return {
        $noticePopup,
        $notice,
        $noticeInitial,
        $noticeLoading,
        $noticeRefreshLoading,
        dispatch_refreshNotice,
        toggleNoticePopup,
    }
})

export const useProtocolStore = defineStore('protocol', () => {
    const { res: $protocol } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E4%BF%A1%E6%81%AF/listUsingGET_1
        url: '/appInfo/list',
        initialValues: [],
        sessionKey: 'protocol',
    })

    return {
        $protocol,
    }
})

export const useSysConfigStore = defineStore('sysConfig', () => {
    const [ {
        res: $sysConfig,
        loading: $sysConfigLoading,
        onRefresh: dispatch_refreshSysConfig,
    }, {
        clearIntervalInstance: dispatch_clearSysConfigInterval,
    } ] = useIntervalFetch({
        url: API_PATH.SYS_CONFIG,
        initialValues: {
            siteId: 0,
            tradingMode: 3, // Default to 3 (股票+期货) to show all sections
            tradingModeName: '',
            uiColorScheme: 0,
            uiColorSchemeName: '',
            uiTemplate: 0,
            uiTemplateName: '',
        },
        cancellable: false,
        method: FETCH_METHOD.GET,
        sessionKey: 'sysConfig',
    }, {
        config: {
            method: FETCH_INTERVAL_TYPE.REQUEST,
            delay: 30000, // Poll every 30 seconds (less frequent than marketOpenAsset)
        },
    })

    // Computed properties for showing sections based on tradingMode
    const showIndexSection = computed(() => {
        // tradingMode: 1=股票, 2=期货, 3=股票+期货
        // Index should show when stocks are enabled (1 or 3)
        return [ 1, 3 ].includes($sysConfig.value?.tradingMode)
    })

    const showFuturesSection = computed(() => {
        // tradingMode: 1=股票, 2=期货, 3=股票+期货
        // Futures should show when futures are enabled (2 or 3)
        return [ 2, 3 ].includes($sysConfig.value?.tradingMode)
    })

    return {
        $sysConfig,
        $sysConfigLoading,
        dispatch_refreshSysConfig,
        dispatch_clearSysConfigInterval,
        showIndexSection,
        showFuturesSection,
    }
})
