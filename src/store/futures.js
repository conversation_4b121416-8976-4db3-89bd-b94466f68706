import { futuresInfoInitial, onGetFuturesInfo } from '@/hooks/futures.js'
import { STOCK_ROUTE } from '@/config/index.js'

const volumeInitial = {
    ask: [], bid: [], symbol: '', market: '', securityType: '', close: 0, latestPrice: 0,
}

export const useFuturesStore = defineStore('futures', () => {
    const router1 = useRouter()
    const { push, replace } = router1

    const $futuresType = ref('DCE')
    const $futuresActiveTab = ref('futures-transaction')
    const $minuteType = ref('realtime_day')
    const $currentFutures = useSessionStorage('currentFutures', {})

    // 安全的 instrument 拼接
    const $futuresInstrument = computed(() => {
        const { market, securityType, symbol } = $currentFutures.value || {}
        if (!market || !securityType || !symbol) return ''
        return [ market, securityType, symbol ].join('|')
    })

    const $futures = ref({
        ...futuresInfoInitial,
    })

    const isValidInstrument = (instrument) => {
        return instrument && typeof instrument === 'string' && instrument.split('|').length === 3 && !instrument.includes('undefined') && !instrument.includes('null')
    }

    const dispatch_checkFutures = async (futuresInfo, option) => {
        const { routeName = STOCK_ROUTE.DETAILS, isReplace, params, redirect = true, isBack = false } = option ?? {}

        // 更新当前期货
        $currentFutures.value = futuresInfo
        const instrument = $futuresInstrument.value

        if (!isValidInstrument(instrument)) {
            console.warn('无效的 instrument:', instrument)
            return
        }

        const fetches = [
            dispatch_getFutures({ instrument }),
            volumeRun({ instrument, depth: 10 }),
        ]
        // 可选的跳转逻辑（注释保留）
        if (redirect) {
            const router = isReplace ? replace : push
            if (isBack) {
                router1.back()
            } else {
                const path = `/futures/${routeName}`
                if (routeName === STOCK_ROUTE.DETAILS) {
                    sessionStorage.setItem('futuresDetailsTab', 'futures')
                }
                fetches.push(router({ path, query: params }))
            }
        }

        await Promise.all(fetches)
    }

    const dispatch_getFutures = async (params) => {
        $futures.value = await onGetFuturesInfo(params.value)
    }

    const $raise_fall = computed(() => {
        const gain = $currentFutures.value.gain
        return {
            raise: gain > 0,
            fall: gain < 0,
            flat: gain === 0,
            color: utils_amount_color(gain),
        }
    })

    const { res: $volume, run: volumeRun } = useRequest({
        url: '/futures/market/depth/l2',
        manual: true,
        initialValues: { ...volumeInitial },
        cancellable: false,
    })

    const dispatch_refreshFutures = () => {
        const instrument = $futuresInstrument.value
        if (!isValidInstrument(instrument)) {
            console.warn('刷新时 instrument 无效:', instrument)
            return
        }
        dispatch_checkFutures($currentFutures.value, { redirect: false })
    }

    dispatch_refreshFutures()

    const dispatch_get_futures_detail = async () => {
        const instrument = $futuresInstrument.value
        if (!isValidInstrument(instrument)) {
            console.warn('获取详情时 instrument 无效:', instrument)
            return
        }

        $currentFutures.value = await api_get({
            url: '/futures/market/stockInfo',
            params: { instrument },
        })
    }

    const dispatch_futures_activeTab = async (v) => {
        $futuresActiveTab.value = v
    }
    
    return {
        $futuresInstrument,
        $currentFutures,
        $futures,
        $raise_fall,
        $futuresType,
        $volume,
        $futuresActiveTab,
        $minuteType,

        dispatch_checkFutures,
        dispatch_refreshFutures,
        dispatch_futures_activeTab,
        dispatch_get_futures_detail,
        dispatch_getFutures,
    }
}, {
    persist: {
        key: '_futures_',
        storage: sessionStorage,
        pick: [ '$futuresType', '$currentFutures', '$futuresActiveTab', '$futures', '$volume' ],
        debugger: true,
    },
})
