export const messageActiveTab = useSessionStorage('messageActiveTab', 'system')

export const useMessageStore = defineStore('message', () => {

    const { res: $unreadCount, onRefresh: dispatch_refreshUnreadCount } = useRequest({
        url: '/message/unreadCount',
        needLogin: true,
        cancellable: false,
        initialValues: 0,
    })

    return {
        $unreadCount,
        dispatch_refreshUnreadCount,
    }
})
