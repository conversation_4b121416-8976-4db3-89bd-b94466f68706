/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './index.html',
        './src/**/*.{vue,js,ts,jsx,tsx}',
    ],
    theme: {
        extend: {
            // Enhanced color palette similar to v4
            colors: {
                // From @theme mapping
                'bg1': 'var(--bg-1)',
                'bg3': 'var(--bg-3)',
                'bg4': 'var(--bg-4)',
                'text1': 'var(--text1)',
                'text2': 'var(--text2)',
                'text3': 'var(--text3)',
                'regular': 'var(--regular)',
                'select': 'var(--select)',
                primary: 'var(--primary)',
                raise: 'var(--raise)',
                fall: 'var(--fall)',
                red: 'var(--red)',
                green: 'var(--green)',
                page: 'var(--page)',
                bg: 'var(--bg)',
                border: 'var(--border)',
                title: 'var(--title)',
                paragraph: 'var(--paragraph)',
                text: 'var(--text)',
                active: 'var(--active)',
                link: 'var(--link)',
                controller_bg: 'var(--controller_bg)',
                input: 'var(--input)',
                card: 'var(--card)',
                special: 'var(--special)',
                tag: 'var(--tag)',
                'red-text': 'var(--red-text)',
                'futures-red': 'var(--futures-red)',
                'futures-green': 'var(--futures-green)',
                'futures-switch': 'var(--futures-switch)',
                'tab-color': 'var(--tab-color)',
                // Gray scale with more variants
                gray: {
                    25: '#fcfcfd',
                    50: '#f9fafb',
                    100: '#f2f4f7',
                    200: '#eaecf0',
                    300: '#d0d5dd',
                    400: '#98a2b3',
                    500: '#667085',
                    600: '#475467',
                    700: '#344054',
                    800: '#1d2939',
                    900: '#101828',
                    950: '#0c111d',
                },
                // Enhanced blue palette
                blue: {
                    25: '#f5f8ff',
                    50: '#eff4ff',
                    100: '#d1e0ff',
                    200: '#b2ccff',
                    300: '#84adff',
                    400: '#528bff',
                    500: '#2970ff',
                    600: '#155eef',
                    700: '#004eeb',
                    800: '#0040c1',
                    900: '#00359e',
                    950: '#002266',
                },
                // Enhanced green palette
                green: {
                    25: '#f6fef9',
                    50: '#ecfdf3',
                    100: '#d1fadf',
                    200: '#a6f4c5',
                    300: '#6ce9a6',
                    400: '#32d583',
                    500: '#12b76a',
                    600: '#039855',
                    700: '#027a48',
                    800: '#05603a',
                    900: '#054f31',
                    950: '#022c22',
                },
                // Enhanced red palette
                red: {
                    25: '#fffbfa',
                    50: '#fef3f2',
                    100: '#fee4e2',
                    200: '#fecdca',
                    300: '#fda29b',
                    400: '#f97066',
                    500: '#f04438',
                    600: '#d92d20',
                    700: '#b42318',
                    800: '#912018',
                    900: '#7a271a',
                    950: '#55160c',
                },
                // Enhanced yellow palette
                yellow: {
                    25: '#fffcf5',
                    50: '#fffaeb',
                    100: '#fef0c7',
                    200: '#fedf89',
                    300: '#fec84b',
                    400: '#fdb022',
                    500: '#f79009',
                    600: '#dc6803',
                    700: '#b54708',
                    800: '#93370d',
                    900: '#7a2e0e',
                    950: '#4e1d09',
                },
                // Enhanced purple palette
                purple: {
                    25: '#fdfaff',
                    50: '#faf5ff',
                    100: '#f4ebff',
                    200: '#e9d7fe',
                    300: '#d6bbfb',
                    400: '#b692f6',
                    500: '#9e77ed',
                    600: '#7c3aed',
                    700: '#6927da',
                    800: '#5521b5',
                    900: '#44187b',
                    950: '#2d0a4e',
                },
                // Enhanced pink palette
                pink: {
                    25: '#fef7f7',
                    50: '#fdf2f8',
                    100: '#fce7f3',
                    200: '#fbcfe8',
                    300: '#f8b4d9',
                    400: '#f48fb1',
                    500: '#ee5a6f',
                    600: '#e31b54',
                    700: '#c01048',
                    800: '#a11043',
                    900: '#831843',
                    950: '#500724',
                },
                // Enhanced orange palette
                orange: {
                    25: '#fffef5',
                    50: '#fffaeb',
                    100: '#fef0c7',
                    200: '#fed7aa',
                    300: '#feb273',
                    400: '#fd853a',
                    500: '#fb6514',
                    600: '#ec4a0a',
                    700: '#c4320a',
                    800: '#9c2a10',
                    900: '#7e2410',
                    950: '#451a03',
                },
                // New teal palette
                teal: {
                    25: '#f6fefc',
                    50: '#f0fdf9',
                    100: '#ccfbef',
                    200: '#99f6e0',
                    300: '#5eead4',
                    400: '#2dd4bf',
                    500: '#14b8a6',
                    600: '#0d9488',
                    700: '#0f766e',
                    800: '#115e59',
                    900: '#134e4a',
                    950: '#042f2e',
                },
                // Enhanced indigo palette
                indigo: {
                    25: '#f5f8ff',
                    50: '#eef4ff',
                    100: '#e0e7ff',
                    200: '#c7d2fe',
                    300: '#a5b4fc',
                    400: '#818cf8',
                    500: '#6366f1',
                    600: '#4f46e5',
                    700: '#4338ca',
                    800: '#3730a3',
                    900: '#312e81',
                    950: '#1e1b4b',
                },
            },

            // Enhanced spacing scale (0-200 with all numbers) + fractional values
            spacing: (() => {
                const spacing = {}

                // Generate integer spacing from 0 to 200
                for (let i = 0; i <= 200; i++) {
                    if (i === 0) {
                        spacing['0'] = '0px'
                    } else {
                        spacing[i.toString()] = `${i * 0.25}rem`
                    }
                }

                // Add fractional spacing values (0.25, 0.5, 0.75, 1.25, 1.5, 1.75, etc.)
                const fractions = [ 0.25, 0.5, 0.75, 1.25, 1.5, 1.75, 2.25, 2.5, 2.75, 3.25, 3.5, 3.75 ]
                fractions.forEach(fraction => {
                    spacing[fraction.toString()] = `${fraction * 0.25}rem`
                })

                // Add more fractional values up to 10
                for (let i = 4; i <= 10; i++) {
                    const quarters = [ 0.25, 0.5, 0.75 ]
                    quarters.forEach(quarter => {
                        const value = i + quarter
                        spacing[value.toString()] = `${value * 0.25}rem`
                    })
                }

                return spacing
            })(),

            // Enhanced font sizes with line heights
            fontSize: {
                '2xs': [ '0.625rem', { lineHeight: 'var(--tw-leading, calc(0.75 / 0.625))' } ],
                'xs': [ '0.75rem', { lineHeight: 'var(--tw-leading, calc(1 / 0.75))' } ],
                'sm': [ '0.875rem', { lineHeight: 'var(--tw-leading, calc(1.25 / 0.875))' } ],
                'base': [ '1rem', { lineHeight: 'var(--tw-leading, 1.5)' } ],
                'lg': [ '1.125rem', { lineHeight: 'var(--tw-leading, calc(1.75 / 1.125))' } ],
                'xl': [ '1.25rem', { lineHeight: 'var(--tw-leading, calc(1.75 / 1.25))' } ],
                '2xl': [ '1.5rem', { lineHeight: 'var(--tw-leading, calc(2 / 1.5))' } ],
                '3xl': [ '1.875rem', { lineHeight: 'var(--tw-leading, 1.2)' } ],
                '4xl': [ '2.25rem', { lineHeight: 'var(--tw-leading, calc(2.5 / 2.25))' } ],
                '5xl': [ '3rem', { lineHeight: 'var(--tw-leading, 1)' } ],
                '6xl': [ '3.75rem', { lineHeight: 'var(--tw-leading, 1)' } ],
                '7xl': [ '4.5rem', { lineHeight: '1' } ],
                '8xl': [ '6rem', { lineHeight: '1' } ],
                '9xl': [ '8rem', { lineHeight: '1' } ],
            },

            // Enhanced border radius
            borderRadius: {
                'none': '0',
                'sm': '0.125rem',
                'DEFAULT': '0.25rem',
                'md': '0.375rem',
                'lg': '0.5rem',
                'xl': '0.75rem',
                '2xl': '1rem',
                '3xl': '1.5rem',
                '4xl': '2rem',
                '5xl': '2.5rem',
                '6xl': '3rem',
            },

            // Enhanced shadows
            boxShadow: {
                'xs': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                'sm': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                'DEFAULT': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                'md': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
                'lg': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
                'xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                '3xl': '0 35px 60px -15px rgba(0, 0, 0, 0.3)',
                'inner': 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
            },

            // Enhanced animation and keyframes
            keyframes: {
                'fade-in': {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                'fade-out': {
                    '0%': { opacity: '1' },
                    '100%': { opacity: '0' },
                },
                'slide-in-up': {
                    '0%': { transform: 'translateY(100%)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                'slide-in-down': {
                    '0%': { transform: 'translateY(-100%)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                'slide-in-left': {
                    '0%': { transform: 'translateX(-100%)', opacity: '0' },
                    '100%': { transform: 'translateX(0)', opacity: '1' },
                },
                'slide-in-right': {
                    '0%': { transform: 'translateX(100%)', opacity: '0' },
                    '100%': { transform: 'translateX(0)', opacity: '1' },
                },
                'scale-in': {
                    '0%': { transform: 'scale(0.8)', opacity: '0' },
                    '100%': { transform: 'scale(1)', opacity: '1' },
                },
                'bounce-in': {
                    '0%': { transform: 'scale(0.3)', opacity: '0' },
                    '50%': { transform: 'scale(1.05)' },
                    '70%': { transform: 'scale(0.9)' },
                    '100%': { transform: 'scale(1)', opacity: '1' },
                },
                'shake': {
                    '0%, 100%': { transform: 'translateX(0)' },
                    '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-10px)' },
                    '20%, 40%, 60%, 80%': { transform: 'translateX(10px)' },
                },
                'wiggle': {
                    '0%, 100%': { transform: 'rotate(-3deg)' },
                    '50%': { transform: 'rotate(3deg)' },
                },
                'float': {
                    '0%, 100%': { transform: 'translateY(0px)' },
                    '50%': { transform: 'translateY(-20px)' },
                },
                'glow': {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.5' },
                },
            },
            animation: {
                'fade-in': 'fade-in 0.5s ease-out',
                'fade-out': 'fade-out 0.5s ease-out',
                'slide-in-up': 'slide-in-up 0.5s ease-out',
                'slide-in-down': 'slide-in-down 0.5s ease-out',
                'slide-in-left': 'slide-in-left 0.5s ease-out',
                'slide-in-right': 'slide-in-right 0.5s ease-out',
                'scale-in': 'scale-in 0.3s ease-out',
                'bounce-in': 'bounce-in 0.6s ease-out',
                'shake': 'shake 0.82s cubic-bezier(.36,.07,.19,.97) both',
                'wiggle': 'wiggle 1s ease-in-out infinite',
                'float': 'float 3s ease-in-out infinite',
                'glow': 'glow 2s ease-in-out infinite',
            },

            // Enhanced gradients
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
                'gradient-135': 'linear-gradient(135deg, var(--tw-gradient-stops))',
                'gradient-225': 'linear-gradient(225deg, var(--tw-gradient-stops))',
                'gradient-315': 'linear-gradient(315deg, var(--tw-gradient-stops))',
                'gradient-45': 'linear-gradient(45deg, var(--tw-gradient-stops))',
            },

            // Typography
            fontFamily: {
                sans: [ 'Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif' ],
                serif: [ 'ui-serif', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif' ],
                mono: [ 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace' ],
                display: [ 'Cal Sans', 'Inter', 'ui-sans-serif', 'system-ui' ],
            },

            // Enhanced line heights
            lineHeight: {
                '3': '.75rem',
                '4': '1rem',
                '5': '1.25rem',
                '6': '1.5rem',
                '7': '1.75rem',
                '8': '2rem',
                '9': '2.25rem',
                '10': '2.5rem',
                '11': '2.75rem',
                '12': '3rem',
            },

            // Enhanced letter spacing
            letterSpacing: {
                'tightest': '-0.075em',
                'tighter': '-0.05em',
                'tight': '-0.025em',
                'normal': '0em',
                'wide': '0.025em',
                'wider': '0.05em',
                'widest': '0.1em',
            },

            // Enhanced z-index
            zIndex: {
                '1': '1',
                '2': '2',
                '3': '3',
                '4': '4',
                '5': '5',
                '60': '60',
                '70': '70',
                '80': '80',
                '90': '90',
                '100': '100',
            },

            // Container queries support
            containers: {
                '2xs': '16rem',
                'xs': '20rem',
                'sm': '24rem',
                'md': '28rem',
                'lg': '32rem',
                'xl': '36rem',
                '2xl': '42rem',
                '3xl': '48rem',
                '4xl': '56rem',
                '5xl': '64rem',
                '6xl': '72rem',
                '7xl': '80rem',
            },

            // Enhanced blur
            blur: {
                '4xl': '72px',
                '5xl': '96px',
                '6xl': '128px',
            },

            // Enhanced backdrop blur
            backdropBlur: {
                '4xl': '72px',
                '5xl': '96px',
                '6xl': '128px',
            },

            // Enhanced brightness
            brightness: {
                '25': '.25',
                '175': '1.75',
            },

            // Enhanced contrast
            contrast: {
                '25': '.25',
                '175': '1.75',
            },

            // Enhanced saturate
            saturate: {
                '25': '.25',
                '175': '1.75',
            },

            // Enhanced opacity (0-100 with all numbers)
            opacity: (() => {
                const opacity = {}
                for (let i = 0; i <= 100; i++) {
                    opacity[i.toString()] = (i / 100).toString()
                }
                return opacity
            })(),
        },
    },
    variants: {
        extend: {
            margin: [ 'not-last' ], // 显式开启
        },
    },
    plugins: [
        // Plugin to add alpha color support for CSS variables
        function ({ addUtilities, theme }) {
            const alphaUtilities = {}

            // Get all the custom color variables from your theme
            const customColors = [
                'bg1', 'bg3', 'bg4', 'text1', 'text2', 'text3', 'regular', 'select',
                'primary', 'raise', 'fall', 'red', 'green', 'page', 'bg', 'border',
                'title', 'paragraph', 'text', 'active', 'link', 'controller_bg',
                'input', 'card', 'special', 'tag', 'red-text', 'futures-red',
                'futures-green', 'futures-switch', 'tab-color',
            ]

            // Generate alpha utilities for each custom color
            customColors.forEach(colorName => {
                // Generate opacity values from 0 to 100
                for (let opacity = 0; opacity <= 100; opacity += 5) {
                    // Background color
                    alphaUtilities[`.bg-${colorName}\\/${opacity}`] = {
                        backgroundColor: `color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent)`,
                    }

                    // Text color
                    alphaUtilities[`.text-${colorName}\\/${opacity}`] = {
                        color: `color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent)`,
                    }

                    // Border color
                    alphaUtilities[`.border-${colorName}\\/${opacity}`] = {
                        borderColor: `color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent)`,
                    }

                    // Ring color
                    alphaUtilities[`.ring-${colorName}\\/${opacity}`] = {
                        '--tw-ring-color': `color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent)`,
                    }

                    // Shadow color
                    alphaUtilities[`.shadow-${colorName}\\/${opacity}`] = {
                        boxShadow: `0 1px 3px 0 color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent), 0 1px 2px -1px color-mix(in srgb, var(--${colorName.replace('-', '-')}) ${opacity}%, transparent)`,
                    }
                }
            })

            addUtilities(alphaUtilities)
        },

        // Plugin to add line-height CSS variable support
        function ({ addUtilities, theme }) {
            const leadingUtilities = {}

            // Add --tw-leading CSS variable support
            leadingUtilities['.leading-none'] = { '--tw-leading': '1' }
            leadingUtilities['.leading-tight'] = { '--tw-leading': '1.25' }
            leadingUtilities['.leading-snug'] = { '--tw-leading': '1.375' }
            leadingUtilities['.leading-normal'] = { '--tw-leading': '1.5' }
            leadingUtilities['.leading-relaxed'] = { '--tw-leading': '1.625' }
            leadingUtilities['.leading-loose'] = { '--tw-leading': '2' }

            // Add numeric leading values
            for (let i = 3; i <= 12; i++) {
                leadingUtilities[`.leading-${i}`] = { '--tw-leading': `${i * 0.25}rem` }
            }

            addUtilities(leadingUtilities)
        },
    ],
    // Enhanced dark mode support
    darkMode: 'class',
    // Future flags for v4 compatibility
    future: {
        hoverOnlyWhenSupported: true,
    },
    // Experimental features
    experimental: {
        optimizeUniversalDefaults: true,
    },
} 